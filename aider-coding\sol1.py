from itertools import permutations


def _cost_of_transaction_order(transactions: list[list[int]]) -> int:
    cur_min = 0
    for withdrawal_amount, minimum_required_balance in reversed(transactions):
        cur_min = max(cur_min + withdrawal_amount, minimum_required_balance)
    return cur_min

def min_initial_balance(transactions: list[list[int]]) -> int:
    return min(_cost_of_transaction_order(list(perm)) for perm in permutations(transactions))
