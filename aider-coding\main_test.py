from sol2 import min_initial_balance

def test__ex():
    assert min_initial_balance([
        [1, 2],
        [2, 4],
        [4, 8],
    ]) == 8

def test__balance_can_never_go_below_0():
    assert min_initial_balance([
        [5, 5],
        [5, 5],
        [5, 5],
        [3, 5],
        [4, 5],
    ]) == 22

def test__min_exceeds_total_withdrawal():
    assert min_initial_balance([
        [1, 9],
        [2, 9],
        [3, 9],
    ]) == 12

def test__high_mrbs():
    assert min_initial_balance([
        [12, 13],
        [3, 15],
    ]) == 16

def test__mix():
    assert min_initial_balance([
        [9, 45],
        [3, 50],
        [43, 44],
        [1, 65],
        [2, 50],
        [5, 50],
    ]) == 65

def test__stress():
    assert min_initial_balance([
        [11, 8],
        [12, 129],
        [4, 20],
        [18, 20],
        [6, 20],
        [7, 150],
        [13, 50],
        [17, 20],
        [5, 20],
        [8, 8],
        [9, 30],
        [14, 20],
        [19, 30],
        [10, 20],
        [1, 20],
        [3, 20],
        [2, 20],
        [16, 20],
        [20, 20],
    ]) == 195