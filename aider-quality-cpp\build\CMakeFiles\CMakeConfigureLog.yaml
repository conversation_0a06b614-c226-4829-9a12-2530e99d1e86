
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineSystem.cmake:212 (message)"
      - "CMakeLists.txt:2 (project)"
    message: |
      The system is: Windows - 10.0.26100 - AMD64
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:462 (find_file)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:500 (CMAKE_DETERMINE_COMPILER_ID_WRITE)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:8 (CMAKE_DETERMINE_COMPILER_ID_BUILD)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:122 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    mode: "file"
    variable: "src_in"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "CMakeCCompilerId.c.in"
    candidate_directories:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/"
    found: "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeCCompilerId.c.in"
    search_context:
      ENV{PATH}:
        - "C:\\Program Files\\Java\\jdk-21\\bin"
        - "C:\\Program Files\\Common Files\\Oracle\\Java\\javapath"
        - "C:\\Program Files\\Microsoft\\jdk-*********-hotspot\\bin"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\ProgramData\\chocolatey\\bin"
        - "C:\\Program Files\\Microsoft VS Code\\bin"
        - "C:\\Program Files\\Open Steno Project\\Plover 5.0.0.dev1"
        - "C:\\Portable Applications"
        - "C:\\Portable Applications\\yt-dlp"
        - "C:\\Program Files\\Calibre2\\"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "C:\\Portable Applications\\zig"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\pnpm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\"
        - "C:\\Users\\<USER>\\.cargo\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\platform-tools"
        - "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\tools"
        - "C:\\Users\\<USER>\\AppData\\Local\\pnpm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Ollama"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Users\\<USER>\\.deno\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:122 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      MSBuild version 17.13.19+0d9f5a35a for .NET Framework
      Build started 7/10/2025 5:45:14 PM.
      
      Project "C:\\Users\\<USER>\\_Items\\Dev\\GitHub\\scale\\build\\CMakeFiles\\4.1.0-rc1\\CompilerIdC\\CompilerIdC.vcxproj" on node 1 (default targets).
      PrepareForBuild:
        Creating directory "Debug\\".
        Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
        Creating directory "Debug\\CompilerIdC.tlog\\".
      InitializeBuildStatus:
        Creating "Debug\\CompilerIdC.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
        Touching "Debug\\CompilerIdC.tlog\\unsuccessfulbuild".
      ClCompile:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\CL.exe /c /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc143.pdb" /external:W0 /Gd /TC /FC /errorReport:queue CMakeCCompilerId.c
        CMakeCCompilerId.c
      Link:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdC.exe" /INCREMENTAL:NO /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdC.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdC.lib" /MACHINE:X64 Debug\\CMakeCCompilerId.obj
        CompilerIdC.vcxproj -> C:\\Users\\<USER>\\_Items\\Dev\\GitHub\\scale\\build\\CMakeFiles\\4.1.0-rc1\\CompilerIdC\\CompilerIdC.exe
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_C_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_C_COMPILER=C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\Hostx64\\x64\\cl.exe
      FinalizeBuildStatus:
        Deleting file "Debug\\CompilerIdC.tlog\\unsuccessfulbuild".
        Touching "Debug\\CompilerIdC.tlog\\CompilerIdC.lastbuildstate".
      Done Building Project "C:\\Users\\<USER>\\_Items\\Dev\\GitHub\\scale\\build\\CMakeFiles\\4.1.0-rc1\\CompilerIdC\\CompilerIdC.vcxproj" (default targets).
      
      Build succeeded.
          0 Warning(s)
          0 Error(s)
      
      Time Elapsed 00:00:01.27
      
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CompilerIdC.exe"
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CompilerIdC.vcxproj"
      
      The C compiler identification is MSVC, found in:
        C:/Users/<USER>/_Items/Dev/GitHub/scale/build/CMakeFiles/4.1.0-rc1/CompilerIdC/CompilerIdC.exe
      
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:37 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:65 (__resolve_tool_path)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:103 (__resolve_linker_path)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "_CMAKE_TOOL_WITH_PATH"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "link"
    candidate_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/"
      - "C:/Program Files/Java/jdk-21/bin/"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/ProgramData/chocolatey/bin/"
      - "C:/Program Files/Microsoft VS Code/bin/"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/"
      - "C:/Portable Applications/"
      - "C:/Portable Applications/yt-dlp/"
      - "C:/Program Files/Calibre2/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/Docker/Docker/resources/bin/"
      - "C:/Portable Applications/zig/"
      - "C:/Program Files/nodejs/"
      - "C:/Program Files/dotnet/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/"
      - "C:/Users/<USER>/AppData/Local/pnpm/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/"
      - "C:/Users/<USER>/.cargo/bin/"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Users/<USER>/.deno/bin/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
    searched_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/link.com"
    found: "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/link.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Program Files\\Java\\jdk-21\\bin"
        - "C:\\Program Files\\Common Files\\Oracle\\Java\\javapath"
        - "C:\\Program Files\\Microsoft\\jdk-*********-hotspot\\bin"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\ProgramData\\chocolatey\\bin"
        - "C:\\Program Files\\Microsoft VS Code\\bin"
        - "C:\\Program Files\\Open Steno Project\\Plover 5.0.0.dev1"
        - "C:\\Portable Applications"
        - "C:\\Portable Applications\\yt-dlp"
        - "C:\\Program Files\\Calibre2\\"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "C:\\Portable Applications\\zig"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\pnpm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\"
        - "C:\\Users\\<USER>\\.cargo\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\platform-tools"
        - "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\tools"
        - "C:\\Users\\<USER>\\AppData\\Local\\pnpm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Ollama"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Users\\<USER>\\.deno\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:37 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:65 (__resolve_tool_path)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:104 (__resolve_linker_path)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "_CMAKE_TOOL_WITH_PATH"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "lld-link"
    candidate_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/"
      - "C:/Program Files/Java/jdk-21/bin/"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/ProgramData/chocolatey/bin/"
      - "C:/Program Files/Microsoft VS Code/bin/"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/"
      - "C:/Portable Applications/"
      - "C:/Portable Applications/yt-dlp/"
      - "C:/Program Files/Calibre2/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/Docker/Docker/resources/bin/"
      - "C:/Portable Applications/zig/"
      - "C:/Program Files/nodejs/"
      - "C:/Program Files/dotnet/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/"
      - "C:/Users/<USER>/AppData/Local/pnpm/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/"
      - "C:/Users/<USER>/.cargo/bin/"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Users/<USER>/.deno/bin/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
    searched_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/lld-link.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/lld-link.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/lld-link"
      - "C:/Program Files/Java/jdk-21/bin/lld-link.com"
      - "C:/Program Files/Java/jdk-21/bin/lld-link.exe"
      - "C:/Program Files/Java/jdk-21/bin/lld-link"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/lld-link.com"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/lld-link.exe"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/lld-link"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/lld-link.com"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/lld-link.exe"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/lld-link"
      - "C:/Windows/System32/lld-link.com"
      - "C:/Windows/System32/lld-link.exe"
      - "C:/Windows/System32/lld-link"
      - "C:/Windows/lld-link.com"
      - "C:/Windows/lld-link.exe"
      - "C:/Windows/lld-link"
      - "C:/Windows/System32/wbem/lld-link.com"
      - "C:/Windows/System32/wbem/lld-link.exe"
      - "C:/Windows/System32/wbem/lld-link"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/lld-link.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/lld-link.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/lld-link"
      - "C:/Windows/System32/OpenSSH/lld-link.com"
      - "C:/Windows/System32/OpenSSH/lld-link.exe"
      - "C:/Windows/System32/OpenSSH/lld-link"
      - "C:/ProgramData/chocolatey/bin/lld-link.com"
      - "C:/ProgramData/chocolatey/bin/lld-link.exe"
      - "C:/ProgramData/chocolatey/bin/lld-link"
      - "C:/Program Files/Microsoft VS Code/bin/lld-link.com"
      - "C:/Program Files/Microsoft VS Code/bin/lld-link.exe"
      - "C:/Program Files/Microsoft VS Code/bin/lld-link"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/lld-link.com"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/lld-link.exe"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/lld-link"
      - "C:/Portable Applications/lld-link.com"
      - "C:/Portable Applications/lld-link.exe"
      - "C:/Portable Applications/lld-link"
      - "C:/Portable Applications/yt-dlp/lld-link.com"
      - "C:/Portable Applications/yt-dlp/lld-link.exe"
      - "C:/Portable Applications/yt-dlp/lld-link"
      - "C:/Program Files/Calibre2/lld-link.com"
      - "C:/Program Files/Calibre2/lld-link.exe"
      - "C:/Program Files/Calibre2/lld-link"
      - "C:/Program Files/Git/cmd/lld-link.com"
      - "C:/Program Files/Git/cmd/lld-link.exe"
      - "C:/Program Files/Git/cmd/lld-link"
      - "C:/Program Files/Docker/Docker/resources/bin/lld-link.com"
      - "C:/Program Files/Docker/Docker/resources/bin/lld-link.exe"
      - "C:/Program Files/Docker/Docker/resources/bin/lld-link"
      - "C:/Portable Applications/zig/lld-link.com"
      - "C:/Portable Applications/zig/lld-link.exe"
      - "C:/Portable Applications/zig/lld-link"
      - "C:/Program Files/nodejs/lld-link.com"
      - "C:/Program Files/nodejs/lld-link.exe"
      - "C:/Program Files/nodejs/lld-link"
      - "C:/Program Files/dotnet/lld-link.com"
      - "C:/Program Files/dotnet/lld-link.exe"
      - "C:/Program Files/dotnet/lld-link"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/lld-link"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/lld-link"
      - "C:/Users/<USER>/AppData/Local/pnpm/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/pnpm/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/pnpm/lld-link"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/lld-link"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/lld-link"
      - "C:/Users/<USER>/.cargo/bin/lld-link.com"
      - "C:/Users/<USER>/.cargo/bin/lld-link.exe"
      - "C:/Users/<USER>/.cargo/bin/lld-link"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/lld-link"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/lld-link"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/lld-link"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/lld-link"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/lld-link"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/lld-link"
      - "C:/Users/<USER>/.deno/bin/lld-link.com"
      - "C:/Users/<USER>/.deno/bin/lld-link.exe"
      - "C:/Users/<USER>/.deno/bin/lld-link"
      - "C:/Users/<USER>/AppData/Roaming/npm/lld-link.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/lld-link.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/lld-link"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\Program Files\\Java\\jdk-21\\bin"
        - "C:\\Program Files\\Common Files\\Oracle\\Java\\javapath"
        - "C:\\Program Files\\Microsoft\\jdk-*********-hotspot\\bin"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\ProgramData\\chocolatey\\bin"
        - "C:\\Program Files\\Microsoft VS Code\\bin"
        - "C:\\Program Files\\Open Steno Project\\Plover 5.0.0.dev1"
        - "C:\\Portable Applications"
        - "C:\\Portable Applications\\yt-dlp"
        - "C:\\Program Files\\Calibre2\\"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "C:\\Portable Applications\\zig"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\pnpm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\"
        - "C:\\Users\\<USER>\\.cargo\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\platform-tools"
        - "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\tools"
        - "C:\\Users\\<USER>\\AppData\\Local\\pnpm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Ollama"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Users\\<USER>\\.deno\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_LINKER"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "link"
    candidate_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/"
      - "C:/Program Files/Java/jdk-21/bin/"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/ProgramData/chocolatey/bin/"
      - "C:/Program Files/Microsoft VS Code/bin/"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/"
      - "C:/Portable Applications/"
      - "C:/Portable Applications/yt-dlp/"
      - "C:/Program Files/Calibre2/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/Docker/Docker/resources/bin/"
      - "C:/Portable Applications/zig/"
      - "C:/Program Files/nodejs/"
      - "C:/Program Files/dotnet/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/"
      - "C:/Users/<USER>/AppData/Local/pnpm/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/"
      - "C:/Users/<USER>/.cargo/bin/"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Users/<USER>/.deno/bin/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
    searched_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/link.com"
    found: "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/link.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Program Files\\Java\\jdk-21\\bin"
        - "C:\\Program Files\\Common Files\\Oracle\\Java\\javapath"
        - "C:\\Program Files\\Microsoft\\jdk-*********-hotspot\\bin"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\ProgramData\\chocolatey\\bin"
        - "C:\\Program Files\\Microsoft VS Code\\bin"
        - "C:\\Program Files\\Open Steno Project\\Plover 5.0.0.dev1"
        - "C:\\Portable Applications"
        - "C:\\Portable Applications\\yt-dlp"
        - "C:\\Program Files\\Calibre2\\"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "C:\\Portable Applications\\zig"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\pnpm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\"
        - "C:\\Users\\<USER>\\.cargo\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\platform-tools"
        - "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\tools"
        - "C:\\Users\\<USER>\\AppData\\Local\\pnpm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Ollama"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Users\\<USER>\\.deno\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_MT"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "mt"
    candidate_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/"
      - "C:/Program Files/Java/jdk-21/bin/"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/ProgramData/chocolatey/bin/"
      - "C:/Program Files/Microsoft VS Code/bin/"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/"
      - "C:/Portable Applications/"
      - "C:/Portable Applications/yt-dlp/"
      - "C:/Program Files/Calibre2/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/Docker/Docker/resources/bin/"
      - "C:/Portable Applications/zig/"
      - "C:/Program Files/nodejs/"
      - "C:/Program Files/dotnet/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/"
      - "C:/Users/<USER>/AppData/Local/pnpm/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/"
      - "C:/Users/<USER>/.cargo/bin/"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Users/<USER>/.deno/bin/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
    searched_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/mt.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/mt.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/mt"
      - "C:/Program Files/Java/jdk-21/bin/mt.com"
      - "C:/Program Files/Java/jdk-21/bin/mt.exe"
      - "C:/Program Files/Java/jdk-21/bin/mt"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/mt.com"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/mt.exe"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/mt"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/mt.com"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/mt.exe"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/mt"
      - "C:/Windows/System32/mt.com"
      - "C:/Windows/System32/mt.exe"
      - "C:/Windows/System32/mt"
      - "C:/Windows/mt.com"
      - "C:/Windows/mt.exe"
      - "C:/Windows/mt"
      - "C:/Windows/System32/wbem/mt.com"
      - "C:/Windows/System32/wbem/mt.exe"
      - "C:/Windows/System32/wbem/mt"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/mt.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/mt.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/mt"
      - "C:/Windows/System32/OpenSSH/mt.com"
      - "C:/Windows/System32/OpenSSH/mt.exe"
      - "C:/Windows/System32/OpenSSH/mt"
      - "C:/ProgramData/chocolatey/bin/mt.com"
      - "C:/ProgramData/chocolatey/bin/mt.exe"
      - "C:/ProgramData/chocolatey/bin/mt"
      - "C:/Program Files/Microsoft VS Code/bin/mt.com"
      - "C:/Program Files/Microsoft VS Code/bin/mt.exe"
      - "C:/Program Files/Microsoft VS Code/bin/mt"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/mt.com"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/mt.exe"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/mt"
      - "C:/Portable Applications/mt.com"
      - "C:/Portable Applications/mt.exe"
      - "C:/Portable Applications/mt"
      - "C:/Portable Applications/yt-dlp/mt.com"
      - "C:/Portable Applications/yt-dlp/mt.exe"
      - "C:/Portable Applications/yt-dlp/mt"
      - "C:/Program Files/Calibre2/mt.com"
      - "C:/Program Files/Calibre2/mt.exe"
      - "C:/Program Files/Calibre2/mt"
      - "C:/Program Files/Git/cmd/mt.com"
      - "C:/Program Files/Git/cmd/mt.exe"
      - "C:/Program Files/Git/cmd/mt"
      - "C:/Program Files/Docker/Docker/resources/bin/mt.com"
      - "C:/Program Files/Docker/Docker/resources/bin/mt.exe"
      - "C:/Program Files/Docker/Docker/resources/bin/mt"
      - "C:/Portable Applications/zig/mt.com"
      - "C:/Portable Applications/zig/mt.exe"
      - "C:/Portable Applications/zig/mt"
      - "C:/Program Files/nodejs/mt.com"
      - "C:/Program Files/nodejs/mt.exe"
      - "C:/Program Files/nodejs/mt"
      - "C:/Program Files/dotnet/mt.com"
      - "C:/Program Files/dotnet/mt.exe"
      - "C:/Program Files/dotnet/mt"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/mt.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/mt.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/mt"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/mt.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/mt.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/mt"
      - "C:/Users/<USER>/AppData/Local/pnpm/mt.com"
      - "C:/Users/<USER>/AppData/Local/pnpm/mt.exe"
      - "C:/Users/<USER>/AppData/Local/pnpm/mt"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/mt.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/mt.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/mt"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/mt.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/mt.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/mt"
      - "C:/Users/<USER>/.cargo/bin/mt.com"
      - "C:/Users/<USER>/.cargo/bin/mt.exe"
      - "C:/Users/<USER>/.cargo/bin/mt"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/mt.com"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/mt.exe"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/mt"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/mt.com"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/mt.exe"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/mt"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/mt.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/mt.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/mt"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/mt.com"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/mt.exe"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/mt"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/mt.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/mt.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/mt"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/mt.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/mt.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/mt"
      - "C:/Users/<USER>/.deno/bin/mt.com"
      - "C:/Users/<USER>/.deno/bin/mt.exe"
      - "C:/Users/<USER>/.deno/bin/mt"
      - "C:/Users/<USER>/AppData/Roaming/npm/mt.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/mt.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/mt"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\Program Files\\Java\\jdk-21\\bin"
        - "C:\\Program Files\\Common Files\\Oracle\\Java\\javapath"
        - "C:\\Program Files\\Microsoft\\jdk-*********-hotspot\\bin"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\ProgramData\\chocolatey\\bin"
        - "C:\\Program Files\\Microsoft VS Code\\bin"
        - "C:\\Program Files\\Open Steno Project\\Plover 5.0.0.dev1"
        - "C:\\Portable Applications"
        - "C:\\Portable Applications\\yt-dlp"
        - "C:\\Program Files\\Calibre2\\"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "C:\\Portable Applications\\zig"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\pnpm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\"
        - "C:\\Users\\<USER>\\.cargo\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\platform-tools"
        - "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\tools"
        - "C:\\Users\\<USER>\\AppData\\Local\\pnpm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Ollama"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Users\\<USER>\\.deno\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_AR"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "lib"
    candidate_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/"
      - "C:/Program Files/Java/jdk-21/bin/"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/ProgramData/chocolatey/bin/"
      - "C:/Program Files/Microsoft VS Code/bin/"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/"
      - "C:/Portable Applications/"
      - "C:/Portable Applications/yt-dlp/"
      - "C:/Program Files/Calibre2/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/Docker/Docker/resources/bin/"
      - "C:/Portable Applications/zig/"
      - "C:/Program Files/nodejs/"
      - "C:/Program Files/dotnet/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/"
      - "C:/Users/<USER>/AppData/Local/pnpm/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/"
      - "C:/Users/<USER>/.cargo/bin/"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Users/<USER>/.deno/bin/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
    searched_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/lib.com"
    found: "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/lib.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Program Files\\Java\\jdk-21\\bin"
        - "C:\\Program Files\\Common Files\\Oracle\\Java\\javapath"
        - "C:\\Program Files\\Microsoft\\jdk-*********-hotspot\\bin"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\ProgramData\\chocolatey\\bin"
        - "C:\\Program Files\\Microsoft VS Code\\bin"
        - "C:\\Program Files\\Open Steno Project\\Plover 5.0.0.dev1"
        - "C:\\Portable Applications"
        - "C:\\Portable Applications\\yt-dlp"
        - "C:\\Program Files\\Calibre2\\"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "C:\\Portable Applications\\zig"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\pnpm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\"
        - "C:\\Users\\<USER>\\.cargo\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\platform-tools"
        - "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\tools"
        - "C:\\Users\\<USER>\\AppData\\Local\\pnpm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Ollama"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Users\\<USER>\\.deno\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:462 (find_file)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:500 (CMAKE_DETERMINE_COMPILER_ID_WRITE)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:8 (CMAKE_DETERMINE_COMPILER_ID_BUILD)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    mode: "file"
    variable: "src_in"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "CMakeCXXCompilerId.cpp.in"
    candidate_directories:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/"
    found: "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeCXXCompilerId.cpp.in"
    search_context:
      ENV{PATH}:
        - "C:\\Program Files\\Java\\jdk-21\\bin"
        - "C:\\Program Files\\Common Files\\Oracle\\Java\\javapath"
        - "C:\\Program Files\\Microsoft\\jdk-*********-hotspot\\bin"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\ProgramData\\chocolatey\\bin"
        - "C:\\Program Files\\Microsoft VS Code\\bin"
        - "C:\\Program Files\\Open Steno Project\\Plover 5.0.0.dev1"
        - "C:\\Portable Applications"
        - "C:\\Portable Applications\\yt-dlp"
        - "C:\\Program Files\\Calibre2\\"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "C:\\Portable Applications\\zig"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\pnpm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\"
        - "C:\\Users\\<USER>\\.cargo\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\platform-tools"
        - "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\tools"
        - "C:\\Users\\<USER>\\AppData\\Local\\pnpm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Ollama"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Users\\<USER>\\.deno\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      MSBuild version 17.13.19+0d9f5a35a for .NET Framework
      Build started 7/10/2025 5:45:16 PM.
      
      Project "C:\\Users\\<USER>\\_Items\\Dev\\GitHub\\scale\\build\\CMakeFiles\\4.1.0-rc1\\CompilerIdCXX\\CompilerIdCXX.vcxproj" on node 1 (default targets).
      PrepareForBuild:
        Creating directory "Debug\\".
        Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
        Creating directory "Debug\\CompilerIdCXX.tlog\\".
      InitializeBuildStatus:
        Creating "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
        Touching "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild".
      ClCompile:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\CL.exe /c /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc143.pdb" /external:W0 /Gd /TP /FC /errorReport:queue CMakeCXXCompilerId.cpp
        CMakeCXXCompilerId.cpp
      Link:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdCXX.exe" /INCREMENTAL:NO /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdCXX.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdCXX.lib" /MACHINE:X64 Debug\\CMakeCXXCompilerId.obj
        CompilerIdCXX.vcxproj -> C:\\Users\\<USER>\\_Items\\Dev\\GitHub\\scale\\build\\CMakeFiles\\4.1.0-rc1\\CompilerIdCXX\\CompilerIdCXX.exe
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_CXX_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_CXX_COMPILER=C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\Hostx64\\x64\\cl.exe
      FinalizeBuildStatus:
        Deleting file "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild".
        Touching "Debug\\CompilerIdCXX.tlog\\CompilerIdCXX.lastbuildstate".
      Done Building Project "C:\\Users\\<USER>\\_Items\\Dev\\GitHub\\scale\\build\\CMakeFiles\\4.1.0-rc1\\CompilerIdCXX\\CompilerIdCXX.vcxproj" (default targets).
      
      Build succeeded.
          0 Warning(s)
          0 Error(s)
      
      Time Elapsed 00:00:00.69
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.exe"
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.vcxproj"
      
      The CXX compiler identification is MSVC, found in:
        C:/Users/<USER>/_Items/Dev/GitHub/scale/build/CMakeFiles/4.1.0-rc1/CompilerIdCXX/CompilerIdCXX.exe
      
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:37 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:65 (__resolve_tool_path)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:104 (__resolve_linker_path)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:206 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "_CMAKE_TOOL_WITH_PATH"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "lld-link"
    candidate_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/"
      - "C:/Program Files/Java/jdk-21/bin/"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/ProgramData/chocolatey/bin/"
      - "C:/Program Files/Microsoft VS Code/bin/"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/"
      - "C:/Portable Applications/"
      - "C:/Portable Applications/yt-dlp/"
      - "C:/Program Files/Calibre2/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/Docker/Docker/resources/bin/"
      - "C:/Portable Applications/zig/"
      - "C:/Program Files/nodejs/"
      - "C:/Program Files/dotnet/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/"
      - "C:/Users/<USER>/AppData/Local/pnpm/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/"
      - "C:/Users/<USER>/.cargo/bin/"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Users/<USER>/.deno/bin/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
    searched_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/lld-link.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/lld-link.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/lld-link"
      - "C:/Program Files/Java/jdk-21/bin/lld-link.com"
      - "C:/Program Files/Java/jdk-21/bin/lld-link.exe"
      - "C:/Program Files/Java/jdk-21/bin/lld-link"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/lld-link.com"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/lld-link.exe"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/lld-link"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/lld-link.com"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/lld-link.exe"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/lld-link"
      - "C:/Windows/System32/lld-link.com"
      - "C:/Windows/System32/lld-link.exe"
      - "C:/Windows/System32/lld-link"
      - "C:/Windows/lld-link.com"
      - "C:/Windows/lld-link.exe"
      - "C:/Windows/lld-link"
      - "C:/Windows/System32/wbem/lld-link.com"
      - "C:/Windows/System32/wbem/lld-link.exe"
      - "C:/Windows/System32/wbem/lld-link"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/lld-link.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/lld-link.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/lld-link"
      - "C:/Windows/System32/OpenSSH/lld-link.com"
      - "C:/Windows/System32/OpenSSH/lld-link.exe"
      - "C:/Windows/System32/OpenSSH/lld-link"
      - "C:/ProgramData/chocolatey/bin/lld-link.com"
      - "C:/ProgramData/chocolatey/bin/lld-link.exe"
      - "C:/ProgramData/chocolatey/bin/lld-link"
      - "C:/Program Files/Microsoft VS Code/bin/lld-link.com"
      - "C:/Program Files/Microsoft VS Code/bin/lld-link.exe"
      - "C:/Program Files/Microsoft VS Code/bin/lld-link"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/lld-link.com"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/lld-link.exe"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/lld-link"
      - "C:/Portable Applications/lld-link.com"
      - "C:/Portable Applications/lld-link.exe"
      - "C:/Portable Applications/lld-link"
      - "C:/Portable Applications/yt-dlp/lld-link.com"
      - "C:/Portable Applications/yt-dlp/lld-link.exe"
      - "C:/Portable Applications/yt-dlp/lld-link"
      - "C:/Program Files/Calibre2/lld-link.com"
      - "C:/Program Files/Calibre2/lld-link.exe"
      - "C:/Program Files/Calibre2/lld-link"
      - "C:/Program Files/Git/cmd/lld-link.com"
      - "C:/Program Files/Git/cmd/lld-link.exe"
      - "C:/Program Files/Git/cmd/lld-link"
      - "C:/Program Files/Docker/Docker/resources/bin/lld-link.com"
      - "C:/Program Files/Docker/Docker/resources/bin/lld-link.exe"
      - "C:/Program Files/Docker/Docker/resources/bin/lld-link"
      - "C:/Portable Applications/zig/lld-link.com"
      - "C:/Portable Applications/zig/lld-link.exe"
      - "C:/Portable Applications/zig/lld-link"
      - "C:/Program Files/nodejs/lld-link.com"
      - "C:/Program Files/nodejs/lld-link.exe"
      - "C:/Program Files/nodejs/lld-link"
      - "C:/Program Files/dotnet/lld-link.com"
      - "C:/Program Files/dotnet/lld-link.exe"
      - "C:/Program Files/dotnet/lld-link"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/lld-link"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/lld-link"
      - "C:/Users/<USER>/AppData/Local/pnpm/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/pnpm/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/pnpm/lld-link"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/lld-link"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/lld-link"
      - "C:/Users/<USER>/.cargo/bin/lld-link.com"
      - "C:/Users/<USER>/.cargo/bin/lld-link.exe"
      - "C:/Users/<USER>/.cargo/bin/lld-link"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/lld-link"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/lld-link"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/lld-link"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/lld-link"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/lld-link"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/lld-link"
      - "C:/Users/<USER>/.deno/bin/lld-link.com"
      - "C:/Users/<USER>/.deno/bin/lld-link.exe"
      - "C:/Users/<USER>/.deno/bin/lld-link"
      - "C:/Users/<USER>/AppData/Roaming/npm/lld-link.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/lld-link.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/lld-link"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\Program Files\\Java\\jdk-21\\bin"
        - "C:\\Program Files\\Common Files\\Oracle\\Java\\javapath"
        - "C:\\Program Files\\Microsoft\\jdk-*********-hotspot\\bin"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\ProgramData\\chocolatey\\bin"
        - "C:\\Program Files\\Microsoft VS Code\\bin"
        - "C:\\Program Files\\Open Steno Project\\Plover 5.0.0.dev1"
        - "C:\\Portable Applications"
        - "C:\\Portable Applications\\yt-dlp"
        - "C:\\Program Files\\Calibre2\\"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "C:\\Portable Applications\\zig"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\pnpm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\"
        - "C:\\Users\\<USER>\\.cargo\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\platform-tools"
        - "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\tools"
        - "C:\\Users\\<USER>\\AppData\\Local\\pnpm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Ollama"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Users\\<USER>\\.deno\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineRCCompiler.cmake:40 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Platform/Windows-MSVC.cmake:573 (enable_language)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Platform/Windows-MSVC.cmake:546 (__windows_compiler_msvc_enable_rc)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Platform/Windows-MSVC-C.cmake:5 (__windows_compiler_msvc)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeCInformation.cmake:48 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_RC_COMPILER"
    description: "RC compiler"
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "rc"
    candidate_directories:
      - "C:/Program Files/Java/jdk-21/bin/"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/ProgramData/chocolatey/bin/"
      - "C:/Program Files/Microsoft VS Code/bin/"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/"
      - "C:/Portable Applications/"
      - "C:/Portable Applications/yt-dlp/"
      - "C:/Program Files/Calibre2/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/Docker/Docker/resources/bin/"
      - "C:/Portable Applications/zig/"
      - "C:/Program Files/nodejs/"
      - "C:/Program Files/dotnet/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/"
      - "C:/Users/<USER>/AppData/Local/pnpm/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/"
      - "C:/Users/<USER>/.cargo/bin/"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Users/<USER>/.deno/bin/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "C:/Program Files/bin/"
      - "C:/Program Files/sbin/"
      - "C:/Program Files/"
      - "C:/Program Files (x86)/bin/"
      - "C:/Program Files (x86)/sbin/"
      - "C:/Program Files (x86)/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Program Files/CMake/sbin/"
      - "C:/Program Files/CMake/"
    searched_directories:
      - "C:/Program Files/Java/jdk-21/bin/rc.com"
      - "C:/Program Files/Java/jdk-21/bin/rc.exe"
      - "C:/Program Files/Java/jdk-21/bin/rc"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/rc.com"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/rc.exe"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/rc"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/rc.com"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/rc.exe"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/rc"
      - "C:/Windows/System32/rc.com"
      - "C:/Windows/System32/rc.exe"
      - "C:/Windows/System32/rc"
      - "C:/Windows/rc.com"
      - "C:/Windows/rc.exe"
      - "C:/Windows/rc"
      - "C:/Windows/System32/wbem/rc.com"
      - "C:/Windows/System32/wbem/rc.exe"
      - "C:/Windows/System32/wbem/rc"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/rc.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/rc.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/rc"
      - "C:/Windows/System32/OpenSSH/rc.com"
      - "C:/Windows/System32/OpenSSH/rc.exe"
      - "C:/Windows/System32/OpenSSH/rc"
      - "C:/ProgramData/chocolatey/bin/rc.com"
      - "C:/ProgramData/chocolatey/bin/rc.exe"
      - "C:/ProgramData/chocolatey/bin/rc"
      - "C:/Program Files/Microsoft VS Code/bin/rc.com"
      - "C:/Program Files/Microsoft VS Code/bin/rc.exe"
      - "C:/Program Files/Microsoft VS Code/bin/rc"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/rc.com"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/rc.exe"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/rc"
      - "C:/Portable Applications/rc.com"
      - "C:/Portable Applications/rc.exe"
      - "C:/Portable Applications/rc"
      - "C:/Portable Applications/yt-dlp/rc.com"
      - "C:/Portable Applications/yt-dlp/rc.exe"
      - "C:/Portable Applications/yt-dlp/rc"
      - "C:/Program Files/Calibre2/rc.com"
      - "C:/Program Files/Calibre2/rc.exe"
      - "C:/Program Files/Calibre2/rc"
      - "C:/Program Files/Git/cmd/rc.com"
      - "C:/Program Files/Git/cmd/rc.exe"
      - "C:/Program Files/Git/cmd/rc"
      - "C:/Program Files/Docker/Docker/resources/bin/rc.com"
      - "C:/Program Files/Docker/Docker/resources/bin/rc.exe"
      - "C:/Program Files/Docker/Docker/resources/bin/rc"
      - "C:/Portable Applications/zig/rc.com"
      - "C:/Portable Applications/zig/rc.exe"
      - "C:/Portable Applications/zig/rc"
      - "C:/Program Files/nodejs/rc.com"
      - "C:/Program Files/nodejs/rc.exe"
      - "C:/Program Files/nodejs/rc"
      - "C:/Program Files/dotnet/rc.com"
      - "C:/Program Files/dotnet/rc.exe"
      - "C:/Program Files/dotnet/rc"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/rc.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/rc.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/rc"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/rc.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/rc.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/rc"
      - "C:/Users/<USER>/AppData/Local/pnpm/rc.com"
      - "C:/Users/<USER>/AppData/Local/pnpm/rc.exe"
      - "C:/Users/<USER>/AppData/Local/pnpm/rc"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/rc.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/rc.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/rc"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/rc.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/rc.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/rc"
      - "C:/Users/<USER>/.cargo/bin/rc.com"
      - "C:/Users/<USER>/.cargo/bin/rc.exe"
      - "C:/Users/<USER>/.cargo/bin/rc"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/rc.com"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/rc.exe"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/rc"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/rc.com"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/rc.exe"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/rc"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/rc.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/rc.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/rc"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/rc.com"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/rc.exe"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/rc"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/rc.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/rc.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/rc"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/rc.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/rc.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/rc"
      - "C:/Users/<USER>/.deno/bin/rc.com"
      - "C:/Users/<USER>/.deno/bin/rc.exe"
      - "C:/Users/<USER>/.deno/bin/rc"
      - "C:/Users/<USER>/AppData/Roaming/npm/rc.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/rc.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/rc"
      - "C:/Program Files/bin/rc.com"
      - "C:/Program Files/bin/rc.exe"
      - "C:/Program Files/bin/rc"
      - "C:/Program Files/sbin/rc.com"
      - "C:/Program Files/sbin/rc.exe"
      - "C:/Program Files/sbin/rc"
      - "C:/Program Files/rc.com"
      - "C:/Program Files/rc.exe"
      - "C:/Program Files/rc"
      - "C:/Program Files (x86)/bin/rc.com"
      - "C:/Program Files (x86)/bin/rc.exe"
      - "C:/Program Files (x86)/bin/rc"
      - "C:/Program Files (x86)/sbin/rc.com"
      - "C:/Program Files (x86)/sbin/rc.exe"
      - "C:/Program Files (x86)/sbin/rc"
      - "C:/Program Files (x86)/rc.com"
      - "C:/Program Files (x86)/rc.exe"
      - "C:/Program Files (x86)/rc"
      - "C:/Program Files/CMake/bin/rc.com"
      - "C:/Program Files/CMake/bin/rc.exe"
      - "C:/Program Files/CMake/bin/rc"
      - "C:/Program Files/CMake/sbin/rc.com"
      - "C:/Program Files/CMake/sbin/rc.exe"
      - "C:/Program Files/CMake/sbin/rc"
      - "C:/Program Files/CMake/rc.com"
      - "C:/Program Files/CMake/rc.exe"
      - "C:/Program Files/CMake/rc"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\Program Files\\Java\\jdk-21\\bin"
        - "C:\\Program Files\\Common Files\\Oracle\\Java\\javapath"
        - "C:\\Program Files\\Microsoft\\jdk-*********-hotspot\\bin"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\ProgramData\\chocolatey\\bin"
        - "C:\\Program Files\\Microsoft VS Code\\bin"
        - "C:\\Program Files\\Open Steno Project\\Plover 5.0.0.dev1"
        - "C:\\Portable Applications"
        - "C:\\Portable Applications\\yt-dlp"
        - "C:\\Program Files\\Calibre2\\"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "C:\\Portable Applications\\zig"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\pnpm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\"
        - "C:\\Users\\<USER>\\.cargo\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\platform-tools"
        - "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\tools"
        - "C:\\Users\\<USER>\\AppData\\Local\\pnpm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Ollama"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Users\\<USER>\\.deno\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
      CMAKE_INSTALL_PREFIX: "C:/Program Files (x86)/."
      CMAKE_SYSTEM_PREFIX_PATH:
        - "C:/Program Files"
        - "C:/Program Files (x86)"
        - "C:/Program Files/CMake"
        - "C:/Program Files (x86)/."
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:83 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting C compiler ABI info"
    directories:
      source: "C:/Users/<USER>/_Items/Dev/GitHub/scale/build/CMakeFiles/CMakeScratch/TryCompile-0g7nt9"
      binary: "C:/Users/<USER>/_Items/Dev/GitHub/scale/build/CMakeFiles/CMakeScratch/TryCompile-0g7nt9"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_C_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/_Items/Dev/GitHub/scale/build/CMakeFiles/CMakeScratch/TryCompile-0g7nt9'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_2a3a6.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.13.19+0d9f5a35a for .NET Framework
        Build started 7/10/2025 5:45:18 PM.
        
        Project "C:\\Users\\<USER>\\_Items\\Dev\\GitHub\\scale\\build\\CMakeFiles\\CMakeScratch\\TryCompile-0g7nt9\\cmTC_2a3a6.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_2a3a6.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "C:\\Users\\<USER>\\_Items\\Dev\\GitHub\\scale\\build\\CMakeFiles\\CMakeScratch\\TryCompile-0g7nt9\\Debug\\".
          Creating directory "cmTC_2a3a6.dir\\Debug\\cmTC_2a3a6.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_2a3a6.dir\\Debug\\cmTC_2a3a6.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_2a3a6.dir\\Debug\\cmTC_2a3a6.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Fo"cmTC_2a3a6.dir\\Debug\\\\" /Fd"cmTC_2a3a6.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-4.1\\Modules\\CMakeCCompilerABI.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.43.34810 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Fo"cmTC_2a3a6.dir\\Debug\\\\" /Fd"cmTC_2a3a6.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-4.1\\Modules\\CMakeCCompilerABI.c"
          CMakeCCompilerABI.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\link.exe /OUT:"C:\\Users\\<USER>\\_Items\\Dev\\GitHub\\scale\\build\\CMakeFiles\\CMakeScratch\\TryCompile-0g7nt9\\Debug\\cmTC_2a3a6.exe" /INCREMENTAL /ILK:"cmTC_2a3a6.dir\\Debug\\cmTC_2a3a6.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/Users/<USER>/_Items/Dev/GitHub/scale/build/CMakeFiles/CMakeScratch/TryCompile-0g7nt9/Debug/cmTC_2a3a6.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /IMPLIB:"C:/Users/<USER>/_Items/Dev/GitHub/scale/build/CMakeFiles/CMakeScratch/TryCompile-0g7nt9/Debug/cmTC_2a3a6.lib" /MACHINE:X64  /machine:x64 cmTC_2a3a6.dir\\Debug\\CMakeCCompilerABI.obj
          cmTC_2a3a6.vcxproj -> C:\\Users\\<USER>\\_Items\\Dev\\GitHub\\scale\\build\\CMakeFiles\\CMakeScratch\\TryCompile-0g7nt9\\Debug\\cmTC_2a3a6.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_2a3a6.dir\\Debug\\cmTC_2a3a6.tlog\\unsuccessfulbuild".
          Touching "cmTC_2a3a6.dir\\Debug\\cmTC_2a3a6.tlog\\cmTC_2a3a6.lastbuildstate".
        Done Building Project "C:\\Users\\<USER>\\_Items\\Dev\\GitHub\\scale\\build\\CMakeFiles\\CMakeScratch\\TryCompile-0g7nt9\\cmTC_2a3a6.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:00.61
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:253 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed C implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|link\\.exe|lld-link(\\.exe)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|link\\.exe|lld-link(\\.exe)?))("|,| |$)]
        linker tool for 'C': C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/HostX64/x64/link.exe
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:299 (cmake_determine_linker_id)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Running the C compiler's linker: "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/HostX64/x64/link.exe" "-v"
      Microsoft (R) Incremental Linker Version 14.43.34810.0
      Copyright (C) Microsoft Corporation.  All rights reserved.
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:83 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "C:/Users/<USER>/_Items/Dev/GitHub/scale/build/CMakeFiles/CMakeScratch/TryCompile-u2xziu"
      binary: "C:/Users/<USER>/_Items/Dev/GitHub/scale/build/CMakeFiles/CMakeScratch/TryCompile-u2xziu"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /W3 /GR /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_CXX_SCAN_FOR_MODULES: "OFF"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/_Items/Dev/GitHub/scale/build/CMakeFiles/CMakeScratch/TryCompile-u2xziu'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_d444d.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.13.19+0d9f5a35a for .NET Framework
        Build started 7/10/2025 5:45:19 PM.
        
        Project "C:\\Users\\<USER>\\_Items\\Dev\\GitHub\\scale\\build\\CMakeFiles\\CMakeScratch\\TryCompile-u2xziu\\cmTC_d444d.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_d444d.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "C:\\Users\\<USER>\\_Items\\Dev\\GitHub\\scale\\build\\CMakeFiles\\CMakeScratch\\TryCompile-u2xziu\\Debug\\".
          Creating directory "cmTC_d444d.dir\\Debug\\cmTC_d444d.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_d444d.dir\\Debug\\cmTC_d444d.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_d444d.dir\\Debug\\cmTC_d444d.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /GR /Fo"cmTC_d444d.dir\\Debug\\\\" /Fd"cmTC_d444d.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TP /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-4.1\\Modules\\CMakeCXXCompilerABI.cpp"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.43.34810 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /GR /Fo"cmTC_d444d.dir\\Debug\\\\" /Fd"cmTC_d444d.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TP /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-4.1\\Modules\\CMakeCXXCompilerABI.cpp"
          CMakeCXXCompilerABI.cpp
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\link.exe /OUT:"C:\\Users\\<USER>\\_Items\\Dev\\GitHub\\scale\\build\\CMakeFiles\\CMakeScratch\\TryCompile-u2xziu\\Debug\\cmTC_d444d.exe" /INCREMENTAL /ILK:"cmTC_d444d.dir\\Debug\\cmTC_d444d.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/Users/<USER>/_Items/Dev/GitHub/scale/build/CMakeFiles/CMakeScratch/TryCompile-u2xziu/Debug/cmTC_d444d.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /IMPLIB:"C:/Users/<USER>/_Items/Dev/GitHub/scale/build/CMakeFiles/CMakeScratch/TryCompile-u2xziu/Debug/cmTC_d444d.lib" /MACHINE:X64  /machine:x64 cmTC_d444d.dir\\Debug\\CMakeCXXCompilerABI.obj
          cmTC_d444d.vcxproj -> C:\\Users\\<USER>\\_Items\\Dev\\GitHub\\scale\\build\\CMakeFiles\\CMakeScratch\\TryCompile-u2xziu\\Debug\\cmTC_d444d.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_d444d.dir\\Debug\\cmTC_d444d.tlog\\unsuccessfulbuild".
          Touching "cmTC_d444d.dir\\Debug\\cmTC_d444d.tlog\\cmTC_d444d.lastbuildstate".
        Done Building Project "C:\\Users\\<USER>\\_Items\\Dev\\GitHub\\scale\\build\\CMakeFiles\\CMakeScratch\\TryCompile-u2xziu\\cmTC_d444d.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:00.56
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:253 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed CXX implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|link\\.exe|lld-link(\\.exe)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|link\\.exe|lld-link(\\.exe)?))("|,| |$)]
        linker tool for 'CXX': C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/HostX64/x64/link.exe
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:299 (cmake_determine_linker_id)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Running the CXX compiler's linker: "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/HostX64/x64/link.exe" "-v"
      Microsoft (R) Incremental Linker Version 14.43.34810.0
      Copyright (C) Microsoft Corporation.  All rights reserved.
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CTest.cmake:188 (find_program)"
      - "CMakeLists.txt:6 (include)"
    mode: "program"
    variable: "MEMORYCHECK_COMMAND"
    description: "Path to the memory checking command, used for memory error detection."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "purify"
      - "valgrind"
      - "boundscheck"
      - "drmemory"
      - "cuda-memcheck"
      - "compute-sanitizer"
    candidate_directories:
      - "C:/Program Files/Java/jdk-21/bin/"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/ProgramData/chocolatey/bin/"
      - "C:/Program Files/Microsoft VS Code/bin/"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/"
      - "C:/Portable Applications/"
      - "C:/Portable Applications/yt-dlp/"
      - "C:/Program Files/Calibre2/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/Docker/Docker/resources/bin/"
      - "C:/Portable Applications/zig/"
      - "C:/Program Files/nodejs/"
      - "C:/Program Files/dotnet/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/"
      - "C:/Users/<USER>/AppData/Local/pnpm/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/"
      - "C:/Users/<USER>/.cargo/bin/"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Users/<USER>/.deno/bin/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "C:/Program Files/bin/"
      - "C:/Program Files/sbin/"
      - "C:/Program Files/"
      - "C:/Program Files (x86)/bin/"
      - "C:/Program Files (x86)/sbin/"
      - "C:/Program Files (x86)/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Program Files/CMake/sbin/"
      - "C:/Program Files/CMake/"
      - "/REGISTRY-NOTFOUND/"
    searched_directories:
      - "C:/Program Files/Java/jdk-21/bin/purify.com"
      - "C:/Program Files/Java/jdk-21/bin/purify.exe"
      - "C:/Program Files/Java/jdk-21/bin/purify"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/purify.com"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/purify.exe"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/purify"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/purify.com"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/purify.exe"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/purify"
      - "C:/Windows/System32/purify.com"
      - "C:/Windows/System32/purify.exe"
      - "C:/Windows/System32/purify"
      - "C:/Windows/purify.com"
      - "C:/Windows/purify.exe"
      - "C:/Windows/purify"
      - "C:/Windows/System32/wbem/purify.com"
      - "C:/Windows/System32/wbem/purify.exe"
      - "C:/Windows/System32/wbem/purify"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/purify.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/purify.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/purify"
      - "C:/Windows/System32/OpenSSH/purify.com"
      - "C:/Windows/System32/OpenSSH/purify.exe"
      - "C:/Windows/System32/OpenSSH/purify"
      - "C:/ProgramData/chocolatey/bin/purify.com"
      - "C:/ProgramData/chocolatey/bin/purify.exe"
      - "C:/ProgramData/chocolatey/bin/purify"
      - "C:/Program Files/Microsoft VS Code/bin/purify.com"
      - "C:/Program Files/Microsoft VS Code/bin/purify.exe"
      - "C:/Program Files/Microsoft VS Code/bin/purify"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/purify.com"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/purify.exe"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/purify"
      - "C:/Portable Applications/purify.com"
      - "C:/Portable Applications/purify.exe"
      - "C:/Portable Applications/purify"
      - "C:/Portable Applications/yt-dlp/purify.com"
      - "C:/Portable Applications/yt-dlp/purify.exe"
      - "C:/Portable Applications/yt-dlp/purify"
      - "C:/Program Files/Calibre2/purify.com"
      - "C:/Program Files/Calibre2/purify.exe"
      - "C:/Program Files/Calibre2/purify"
      - "C:/Program Files/Git/cmd/purify.com"
      - "C:/Program Files/Git/cmd/purify.exe"
      - "C:/Program Files/Git/cmd/purify"
      - "C:/Program Files/Docker/Docker/resources/bin/purify.com"
      - "C:/Program Files/Docker/Docker/resources/bin/purify.exe"
      - "C:/Program Files/Docker/Docker/resources/bin/purify"
      - "C:/Portable Applications/zig/purify.com"
      - "C:/Portable Applications/zig/purify.exe"
      - "C:/Portable Applications/zig/purify"
      - "C:/Program Files/nodejs/purify.com"
      - "C:/Program Files/nodejs/purify.exe"
      - "C:/Program Files/nodejs/purify"
      - "C:/Program Files/dotnet/purify.com"
      - "C:/Program Files/dotnet/purify.exe"
      - "C:/Program Files/dotnet/purify"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/purify.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/purify.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/purify"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/purify.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/purify.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/purify"
      - "C:/Users/<USER>/AppData/Local/pnpm/purify.com"
      - "C:/Users/<USER>/AppData/Local/pnpm/purify.exe"
      - "C:/Users/<USER>/AppData/Local/pnpm/purify"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/purify.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/purify.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/purify"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/purify.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/purify.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/purify"
      - "C:/Users/<USER>/.cargo/bin/purify.com"
      - "C:/Users/<USER>/.cargo/bin/purify.exe"
      - "C:/Users/<USER>/.cargo/bin/purify"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/purify.com"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/purify.exe"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/purify"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/purify.com"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/purify.exe"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/purify"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/purify.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/purify.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/purify"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/purify.com"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/purify.exe"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/purify"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/purify.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/purify.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/purify"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/purify.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/purify.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/purify"
      - "C:/Users/<USER>/.deno/bin/purify.com"
      - "C:/Users/<USER>/.deno/bin/purify.exe"
      - "C:/Users/<USER>/.deno/bin/purify"
      - "C:/Users/<USER>/AppData/Roaming/npm/purify.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/purify.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/purify"
      - "C:/Program Files/bin/purify.com"
      - "C:/Program Files/bin/purify.exe"
      - "C:/Program Files/bin/purify"
      - "C:/Program Files/sbin/purify.com"
      - "C:/Program Files/sbin/purify.exe"
      - "C:/Program Files/sbin/purify"
      - "C:/Program Files/purify.com"
      - "C:/Program Files/purify.exe"
      - "C:/Program Files/purify"
      - "C:/Program Files (x86)/bin/purify.com"
      - "C:/Program Files (x86)/bin/purify.exe"
      - "C:/Program Files (x86)/bin/purify"
      - "C:/Program Files (x86)/sbin/purify.com"
      - "C:/Program Files (x86)/sbin/purify.exe"
      - "C:/Program Files (x86)/sbin/purify"
      - "C:/Program Files (x86)/purify.com"
      - "C:/Program Files (x86)/purify.exe"
      - "C:/Program Files (x86)/purify"
      - "C:/Program Files/CMake/bin/purify.com"
      - "C:/Program Files/CMake/bin/purify.exe"
      - "C:/Program Files/CMake/bin/purify"
      - "C:/Program Files/CMake/sbin/purify.com"
      - "C:/Program Files/CMake/sbin/purify.exe"
      - "C:/Program Files/CMake/sbin/purify"
      - "C:/Program Files/CMake/purify.com"
      - "C:/Program Files/CMake/purify.exe"
      - "C:/Program Files/CMake/purify"
      - "/REGISTRY-NOTFOUND/purify.com"
      - "/REGISTRY-NOTFOUND/purify.exe"
      - "/REGISTRY-NOTFOUND/purify"
      - "C:/Program Files/Java/jdk-21/bin/valgrind.com"
      - "C:/Program Files/Java/jdk-21/bin/valgrind.exe"
      - "C:/Program Files/Java/jdk-21/bin/valgrind"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/valgrind.com"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/valgrind.exe"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/valgrind"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/valgrind.com"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/valgrind.exe"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/valgrind"
      - "C:/Windows/System32/valgrind.com"
      - "C:/Windows/System32/valgrind.exe"
      - "C:/Windows/System32/valgrind"
      - "C:/Windows/valgrind.com"
      - "C:/Windows/valgrind.exe"
      - "C:/Windows/valgrind"
      - "C:/Windows/System32/wbem/valgrind.com"
      - "C:/Windows/System32/wbem/valgrind.exe"
      - "C:/Windows/System32/wbem/valgrind"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/valgrind.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/valgrind.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/valgrind"
      - "C:/Windows/System32/OpenSSH/valgrind.com"
      - "C:/Windows/System32/OpenSSH/valgrind.exe"
      - "C:/Windows/System32/OpenSSH/valgrind"
      - "C:/ProgramData/chocolatey/bin/valgrind.com"
      - "C:/ProgramData/chocolatey/bin/valgrind.exe"
      - "C:/ProgramData/chocolatey/bin/valgrind"
      - "C:/Program Files/Microsoft VS Code/bin/valgrind.com"
      - "C:/Program Files/Microsoft VS Code/bin/valgrind.exe"
      - "C:/Program Files/Microsoft VS Code/bin/valgrind"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/valgrind.com"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/valgrind.exe"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/valgrind"
      - "C:/Portable Applications/valgrind.com"
      - "C:/Portable Applications/valgrind.exe"
      - "C:/Portable Applications/valgrind"
      - "C:/Portable Applications/yt-dlp/valgrind.com"
      - "C:/Portable Applications/yt-dlp/valgrind.exe"
      - "C:/Portable Applications/yt-dlp/valgrind"
      - "C:/Program Files/Calibre2/valgrind.com"
      - "C:/Program Files/Calibre2/valgrind.exe"
      - "C:/Program Files/Calibre2/valgrind"
      - "C:/Program Files/Git/cmd/valgrind.com"
      - "C:/Program Files/Git/cmd/valgrind.exe"
      - "C:/Program Files/Git/cmd/valgrind"
      - "C:/Program Files/Docker/Docker/resources/bin/valgrind.com"
      - "C:/Program Files/Docker/Docker/resources/bin/valgrind.exe"
      - "C:/Program Files/Docker/Docker/resources/bin/valgrind"
      - "C:/Portable Applications/zig/valgrind.com"
      - "C:/Portable Applications/zig/valgrind.exe"
      - "C:/Portable Applications/zig/valgrind"
      - "C:/Program Files/nodejs/valgrind.com"
      - "C:/Program Files/nodejs/valgrind.exe"
      - "C:/Program Files/nodejs/valgrind"
      - "C:/Program Files/dotnet/valgrind.com"
      - "C:/Program Files/dotnet/valgrind.exe"
      - "C:/Program Files/dotnet/valgrind"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/valgrind.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/valgrind.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/valgrind"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/valgrind.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/valgrind.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/valgrind"
      - "C:/Users/<USER>/AppData/Local/pnpm/valgrind.com"
      - "C:/Users/<USER>/AppData/Local/pnpm/valgrind.exe"
      - "C:/Users/<USER>/AppData/Local/pnpm/valgrind"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/valgrind.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/valgrind.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/valgrind"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/valgrind.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/valgrind.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/valgrind"
      - "C:/Users/<USER>/.cargo/bin/valgrind.com"
      - "C:/Users/<USER>/.cargo/bin/valgrind.exe"
      - "C:/Users/<USER>/.cargo/bin/valgrind"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/valgrind.com"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/valgrind.exe"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/valgrind"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/valgrind.com"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/valgrind.exe"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/valgrind"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/valgrind.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/valgrind.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/valgrind"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/valgrind.com"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/valgrind.exe"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/valgrind"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/valgrind.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/valgrind.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/valgrind"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/valgrind.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/valgrind.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/valgrind"
      - "C:/Users/<USER>/.deno/bin/valgrind.com"
      - "C:/Users/<USER>/.deno/bin/valgrind.exe"
      - "C:/Users/<USER>/.deno/bin/valgrind"
      - "C:/Users/<USER>/AppData/Roaming/npm/valgrind.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/valgrind.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/valgrind"
      - "C:/Program Files/bin/valgrind.com"
      - "C:/Program Files/bin/valgrind.exe"
      - "C:/Program Files/bin/valgrind"
      - "C:/Program Files/sbin/valgrind.com"
      - "C:/Program Files/sbin/valgrind.exe"
      - "C:/Program Files/sbin/valgrind"
      - "C:/Program Files/valgrind.com"
      - "C:/Program Files/valgrind.exe"
      - "C:/Program Files/valgrind"
      - "C:/Program Files (x86)/bin/valgrind.com"
      - "C:/Program Files (x86)/bin/valgrind.exe"
      - "C:/Program Files (x86)/bin/valgrind"
      - "C:/Program Files (x86)/sbin/valgrind.com"
      - "C:/Program Files (x86)/sbin/valgrind.exe"
      - "C:/Program Files (x86)/sbin/valgrind"
      - "C:/Program Files (x86)/valgrind.com"
      - "C:/Program Files (x86)/valgrind.exe"
      - "C:/Program Files (x86)/valgrind"
      - "C:/Program Files/CMake/bin/valgrind.com"
      - "C:/Program Files/CMake/bin/valgrind.exe"
      - "C:/Program Files/CMake/bin/valgrind"
      - "C:/Program Files/CMake/sbin/valgrind.com"
      - "C:/Program Files/CMake/sbin/valgrind.exe"
      - "C:/Program Files/CMake/sbin/valgrind"
      - "C:/Program Files/CMake/valgrind.com"
      - "C:/Program Files/CMake/valgrind.exe"
      - "C:/Program Files/CMake/valgrind"
      - "/REGISTRY-NOTFOUND/valgrind.com"
      - "/REGISTRY-NOTFOUND/valgrind.exe"
      - "/REGISTRY-NOTFOUND/valgrind"
      - "C:/Program Files/Java/jdk-21/bin/boundscheck.com"
      - "C:/Program Files/Java/jdk-21/bin/boundscheck.exe"
      - "C:/Program Files/Java/jdk-21/bin/boundscheck"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/boundscheck.com"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/boundscheck.exe"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/boundscheck"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/boundscheck.com"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/boundscheck.exe"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/boundscheck"
      - "C:/Windows/System32/boundscheck.com"
      - "C:/Windows/System32/boundscheck.exe"
      - "C:/Windows/System32/boundscheck"
      - "C:/Windows/boundscheck.com"
      - "C:/Windows/boundscheck.exe"
      - "C:/Windows/boundscheck"
      - "C:/Windows/System32/wbem/boundscheck.com"
      - "C:/Windows/System32/wbem/boundscheck.exe"
      - "C:/Windows/System32/wbem/boundscheck"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/boundscheck.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/boundscheck.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/boundscheck"
      - "C:/Windows/System32/OpenSSH/boundscheck.com"
      - "C:/Windows/System32/OpenSSH/boundscheck.exe"
      - "C:/Windows/System32/OpenSSH/boundscheck"
      - "C:/ProgramData/chocolatey/bin/boundscheck.com"
      - "C:/ProgramData/chocolatey/bin/boundscheck.exe"
      - "C:/ProgramData/chocolatey/bin/boundscheck"
      - "C:/Program Files/Microsoft VS Code/bin/boundscheck.com"
      - "C:/Program Files/Microsoft VS Code/bin/boundscheck.exe"
      - "C:/Program Files/Microsoft VS Code/bin/boundscheck"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/boundscheck.com"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/boundscheck.exe"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/boundscheck"
      - "C:/Portable Applications/boundscheck.com"
      - "C:/Portable Applications/boundscheck.exe"
      - "C:/Portable Applications/boundscheck"
      - "C:/Portable Applications/yt-dlp/boundscheck.com"
      - "C:/Portable Applications/yt-dlp/boundscheck.exe"
      - "C:/Portable Applications/yt-dlp/boundscheck"
      - "C:/Program Files/Calibre2/boundscheck.com"
      - "C:/Program Files/Calibre2/boundscheck.exe"
      - "C:/Program Files/Calibre2/boundscheck"
      - "C:/Program Files/Git/cmd/boundscheck.com"
      - "C:/Program Files/Git/cmd/boundscheck.exe"
      - "C:/Program Files/Git/cmd/boundscheck"
      - "C:/Program Files/Docker/Docker/resources/bin/boundscheck.com"
      - "C:/Program Files/Docker/Docker/resources/bin/boundscheck.exe"
      - "C:/Program Files/Docker/Docker/resources/bin/boundscheck"
      - "C:/Portable Applications/zig/boundscheck.com"
      - "C:/Portable Applications/zig/boundscheck.exe"
      - "C:/Portable Applications/zig/boundscheck"
      - "C:/Program Files/nodejs/boundscheck.com"
      - "C:/Program Files/nodejs/boundscheck.exe"
      - "C:/Program Files/nodejs/boundscheck"
      - "C:/Program Files/dotnet/boundscheck.com"
      - "C:/Program Files/dotnet/boundscheck.exe"
      - "C:/Program Files/dotnet/boundscheck"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/boundscheck.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/boundscheck.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/boundscheck"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/boundscheck.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/boundscheck.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/boundscheck"
      - "C:/Users/<USER>/AppData/Local/pnpm/boundscheck.com"
      - "C:/Users/<USER>/AppData/Local/pnpm/boundscheck.exe"
      - "C:/Users/<USER>/AppData/Local/pnpm/boundscheck"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/boundscheck.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/boundscheck.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/boundscheck"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/boundscheck.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/boundscheck.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/boundscheck"
      - "C:/Users/<USER>/.cargo/bin/boundscheck.com"
      - "C:/Users/<USER>/.cargo/bin/boundscheck.exe"
      - "C:/Users/<USER>/.cargo/bin/boundscheck"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/boundscheck.com"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/boundscheck.exe"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/boundscheck"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/boundscheck.com"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/boundscheck.exe"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/boundscheck"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/boundscheck.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/boundscheck.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/boundscheck"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/boundscheck.com"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/boundscheck.exe"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/boundscheck"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/boundscheck.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/boundscheck.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/boundscheck"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/boundscheck.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/boundscheck.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/boundscheck"
      - "C:/Users/<USER>/.deno/bin/boundscheck.com"
      - "C:/Users/<USER>/.deno/bin/boundscheck.exe"
      - "C:/Users/<USER>/.deno/bin/boundscheck"
      - "C:/Users/<USER>/AppData/Roaming/npm/boundscheck.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/boundscheck.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/boundscheck"
      - "C:/Program Files/bin/boundscheck.com"
      - "C:/Program Files/bin/boundscheck.exe"
      - "C:/Program Files/bin/boundscheck"
      - "C:/Program Files/sbin/boundscheck.com"
      - "C:/Program Files/sbin/boundscheck.exe"
      - "C:/Program Files/sbin/boundscheck"
      - "C:/Program Files/boundscheck.com"
      - "C:/Program Files/boundscheck.exe"
      - "C:/Program Files/boundscheck"
      - "C:/Program Files (x86)/bin/boundscheck.com"
      - "C:/Program Files (x86)/bin/boundscheck.exe"
      - "C:/Program Files (x86)/bin/boundscheck"
      - "C:/Program Files (x86)/sbin/boundscheck.com"
      - "C:/Program Files (x86)/sbin/boundscheck.exe"
      - "C:/Program Files (x86)/sbin/boundscheck"
      - "C:/Program Files (x86)/boundscheck.com"
      - "C:/Program Files (x86)/boundscheck.exe"
      - "C:/Program Files (x86)/boundscheck"
      - "C:/Program Files/CMake/bin/boundscheck.com"
      - "C:/Program Files/CMake/bin/boundscheck.exe"
      - "C:/Program Files/CMake/bin/boundscheck"
      - "C:/Program Files/CMake/sbin/boundscheck.com"
      - "C:/Program Files/CMake/sbin/boundscheck.exe"
      - "C:/Program Files/CMake/sbin/boundscheck"
      - "C:/Program Files/CMake/boundscheck.com"
      - "C:/Program Files/CMake/boundscheck.exe"
      - "C:/Program Files/CMake/boundscheck"
      - "/REGISTRY-NOTFOUND/boundscheck.com"
      - "/REGISTRY-NOTFOUND/boundscheck.exe"
      - "/REGISTRY-NOTFOUND/boundscheck"
      - "C:/Program Files/Java/jdk-21/bin/drmemory.com"
      - "C:/Program Files/Java/jdk-21/bin/drmemory.exe"
      - "C:/Program Files/Java/jdk-21/bin/drmemory"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/drmemory.com"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/drmemory.exe"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/drmemory"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/drmemory.com"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/drmemory.exe"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/drmemory"
      - "C:/Windows/System32/drmemory.com"
      - "C:/Windows/System32/drmemory.exe"
      - "C:/Windows/System32/drmemory"
      - "C:/Windows/drmemory.com"
      - "C:/Windows/drmemory.exe"
      - "C:/Windows/drmemory"
      - "C:/Windows/System32/wbem/drmemory.com"
      - "C:/Windows/System32/wbem/drmemory.exe"
      - "C:/Windows/System32/wbem/drmemory"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/drmemory.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/drmemory.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/drmemory"
      - "C:/Windows/System32/OpenSSH/drmemory.com"
      - "C:/Windows/System32/OpenSSH/drmemory.exe"
      - "C:/Windows/System32/OpenSSH/drmemory"
      - "C:/ProgramData/chocolatey/bin/drmemory.com"
      - "C:/ProgramData/chocolatey/bin/drmemory.exe"
      - "C:/ProgramData/chocolatey/bin/drmemory"
      - "C:/Program Files/Microsoft VS Code/bin/drmemory.com"
      - "C:/Program Files/Microsoft VS Code/bin/drmemory.exe"
      - "C:/Program Files/Microsoft VS Code/bin/drmemory"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/drmemory.com"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/drmemory.exe"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/drmemory"
      - "C:/Portable Applications/drmemory.com"
      - "C:/Portable Applications/drmemory.exe"
      - "C:/Portable Applications/drmemory"
      - "C:/Portable Applications/yt-dlp/drmemory.com"
      - "C:/Portable Applications/yt-dlp/drmemory.exe"
      - "C:/Portable Applications/yt-dlp/drmemory"
      - "C:/Program Files/Calibre2/drmemory.com"
      - "C:/Program Files/Calibre2/drmemory.exe"
      - "C:/Program Files/Calibre2/drmemory"
      - "C:/Program Files/Git/cmd/drmemory.com"
      - "C:/Program Files/Git/cmd/drmemory.exe"
      - "C:/Program Files/Git/cmd/drmemory"
      - "C:/Program Files/Docker/Docker/resources/bin/drmemory.com"
      - "C:/Program Files/Docker/Docker/resources/bin/drmemory.exe"
      - "C:/Program Files/Docker/Docker/resources/bin/drmemory"
      - "C:/Portable Applications/zig/drmemory.com"
      - "C:/Portable Applications/zig/drmemory.exe"
      - "C:/Portable Applications/zig/drmemory"
      - "C:/Program Files/nodejs/drmemory.com"
      - "C:/Program Files/nodejs/drmemory.exe"
      - "C:/Program Files/nodejs/drmemory"
      - "C:/Program Files/dotnet/drmemory.com"
      - "C:/Program Files/dotnet/drmemory.exe"
      - "C:/Program Files/dotnet/drmemory"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/drmemory.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/drmemory.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/drmemory"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/drmemory.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/drmemory.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/drmemory"
      - "C:/Users/<USER>/AppData/Local/pnpm/drmemory.com"
      - "C:/Users/<USER>/AppData/Local/pnpm/drmemory.exe"
      - "C:/Users/<USER>/AppData/Local/pnpm/drmemory"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/drmemory.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/drmemory.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/drmemory"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/drmemory.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/drmemory.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/drmemory"
      - "C:/Users/<USER>/.cargo/bin/drmemory.com"
      - "C:/Users/<USER>/.cargo/bin/drmemory.exe"
      - "C:/Users/<USER>/.cargo/bin/drmemory"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/drmemory.com"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/drmemory.exe"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/drmemory"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/drmemory.com"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/drmemory.exe"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/drmemory"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/drmemory.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/drmemory.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/drmemory"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/drmemory.com"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/drmemory.exe"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/drmemory"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/drmemory.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/drmemory.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/drmemory"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/drmemory.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/drmemory.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/drmemory"
      - "C:/Users/<USER>/.deno/bin/drmemory.com"
      - "C:/Users/<USER>/.deno/bin/drmemory.exe"
      - "C:/Users/<USER>/.deno/bin/drmemory"
      - "C:/Users/<USER>/AppData/Roaming/npm/drmemory.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/drmemory.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/drmemory"
      - "C:/Program Files/bin/drmemory.com"
      - "C:/Program Files/bin/drmemory.exe"
      - "C:/Program Files/bin/drmemory"
      - "C:/Program Files/sbin/drmemory.com"
      - "C:/Program Files/sbin/drmemory.exe"
      - "C:/Program Files/sbin/drmemory"
      - "C:/Program Files/drmemory.com"
      - "C:/Program Files/drmemory.exe"
      - "C:/Program Files/drmemory"
      - "C:/Program Files (x86)/bin/drmemory.com"
      - "C:/Program Files (x86)/bin/drmemory.exe"
      - "C:/Program Files (x86)/bin/drmemory"
      - "C:/Program Files (x86)/sbin/drmemory.com"
      - "C:/Program Files (x86)/sbin/drmemory.exe"
      - "C:/Program Files (x86)/sbin/drmemory"
      - "C:/Program Files (x86)/drmemory.com"
      - "C:/Program Files (x86)/drmemory.exe"
      - "C:/Program Files (x86)/drmemory"
      - "C:/Program Files/CMake/bin/drmemory.com"
      - "C:/Program Files/CMake/bin/drmemory.exe"
      - "C:/Program Files/CMake/bin/drmemory"
      - "C:/Program Files/CMake/sbin/drmemory.com"
      - "C:/Program Files/CMake/sbin/drmemory.exe"
      - "C:/Program Files/CMake/sbin/drmemory"
      - "C:/Program Files/CMake/drmemory.com"
      - "C:/Program Files/CMake/drmemory.exe"
      - "C:/Program Files/CMake/drmemory"
      - "/REGISTRY-NOTFOUND/drmemory.com"
      - "/REGISTRY-NOTFOUND/drmemory.exe"
      - "/REGISTRY-NOTFOUND/drmemory"
      - "C:/Program Files/Java/jdk-21/bin/cuda-memcheck.com"
      - "C:/Program Files/Java/jdk-21/bin/cuda-memcheck.exe"
      - "C:/Program Files/Java/jdk-21/bin/cuda-memcheck"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/cuda-memcheck.com"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/cuda-memcheck.exe"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/cuda-memcheck"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/cuda-memcheck.com"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/cuda-memcheck.exe"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/cuda-memcheck"
      - "C:/Windows/System32/cuda-memcheck.com"
      - "C:/Windows/System32/cuda-memcheck.exe"
      - "C:/Windows/System32/cuda-memcheck"
      - "C:/Windows/cuda-memcheck.com"
      - "C:/Windows/cuda-memcheck.exe"
      - "C:/Windows/cuda-memcheck"
      - "C:/Windows/System32/wbem/cuda-memcheck.com"
      - "C:/Windows/System32/wbem/cuda-memcheck.exe"
      - "C:/Windows/System32/wbem/cuda-memcheck"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/cuda-memcheck.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/cuda-memcheck.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/cuda-memcheck"
      - "C:/Windows/System32/OpenSSH/cuda-memcheck.com"
      - "C:/Windows/System32/OpenSSH/cuda-memcheck.exe"
      - "C:/Windows/System32/OpenSSH/cuda-memcheck"
      - "C:/ProgramData/chocolatey/bin/cuda-memcheck.com"
      - "C:/ProgramData/chocolatey/bin/cuda-memcheck.exe"
      - "C:/ProgramData/chocolatey/bin/cuda-memcheck"
      - "C:/Program Files/Microsoft VS Code/bin/cuda-memcheck.com"
      - "C:/Program Files/Microsoft VS Code/bin/cuda-memcheck.exe"
      - "C:/Program Files/Microsoft VS Code/bin/cuda-memcheck"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/cuda-memcheck.com"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/cuda-memcheck.exe"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/cuda-memcheck"
      - "C:/Portable Applications/cuda-memcheck.com"
      - "C:/Portable Applications/cuda-memcheck.exe"
      - "C:/Portable Applications/cuda-memcheck"
      - "C:/Portable Applications/yt-dlp/cuda-memcheck.com"
      - "C:/Portable Applications/yt-dlp/cuda-memcheck.exe"
      - "C:/Portable Applications/yt-dlp/cuda-memcheck"
      - "C:/Program Files/Calibre2/cuda-memcheck.com"
      - "C:/Program Files/Calibre2/cuda-memcheck.exe"
      - "C:/Program Files/Calibre2/cuda-memcheck"
      - "C:/Program Files/Git/cmd/cuda-memcheck.com"
      - "C:/Program Files/Git/cmd/cuda-memcheck.exe"
      - "C:/Program Files/Git/cmd/cuda-memcheck"
      - "C:/Program Files/Docker/Docker/resources/bin/cuda-memcheck.com"
      - "C:/Program Files/Docker/Docker/resources/bin/cuda-memcheck.exe"
      - "C:/Program Files/Docker/Docker/resources/bin/cuda-memcheck"
      - "C:/Portable Applications/zig/cuda-memcheck.com"
      - "C:/Portable Applications/zig/cuda-memcheck.exe"
      - "C:/Portable Applications/zig/cuda-memcheck"
      - "C:/Program Files/nodejs/cuda-memcheck.com"
      - "C:/Program Files/nodejs/cuda-memcheck.exe"
      - "C:/Program Files/nodejs/cuda-memcheck"
      - "C:/Program Files/dotnet/cuda-memcheck.com"
      - "C:/Program Files/dotnet/cuda-memcheck.exe"
      - "C:/Program Files/dotnet/cuda-memcheck"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/cuda-memcheck.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/cuda-memcheck.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/cuda-memcheck"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/cuda-memcheck.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/cuda-memcheck.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/cuda-memcheck"
      - "C:/Users/<USER>/AppData/Local/pnpm/cuda-memcheck.com"
      - "C:/Users/<USER>/AppData/Local/pnpm/cuda-memcheck.exe"
      - "C:/Users/<USER>/AppData/Local/pnpm/cuda-memcheck"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/cuda-memcheck.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/cuda-memcheck.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/cuda-memcheck"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/cuda-memcheck.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/cuda-memcheck.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/cuda-memcheck"
      - "C:/Users/<USER>/.cargo/bin/cuda-memcheck.com"
      - "C:/Users/<USER>/.cargo/bin/cuda-memcheck.exe"
      - "C:/Users/<USER>/.cargo/bin/cuda-memcheck"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/cuda-memcheck.com"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/cuda-memcheck.exe"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/cuda-memcheck"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/cuda-memcheck.com"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/cuda-memcheck.exe"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/cuda-memcheck"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/cuda-memcheck.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/cuda-memcheck.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/cuda-memcheck"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/cuda-memcheck.com"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/cuda-memcheck.exe"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/cuda-memcheck"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/cuda-memcheck.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/cuda-memcheck.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/cuda-memcheck"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/cuda-memcheck.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/cuda-memcheck.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/cuda-memcheck"
      - "C:/Users/<USER>/.deno/bin/cuda-memcheck.com"
      - "C:/Users/<USER>/.deno/bin/cuda-memcheck.exe"
      - "C:/Users/<USER>/.deno/bin/cuda-memcheck"
      - "C:/Users/<USER>/AppData/Roaming/npm/cuda-memcheck.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/cuda-memcheck.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/cuda-memcheck"
      - "C:/Program Files/bin/cuda-memcheck.com"
      - "C:/Program Files/bin/cuda-memcheck.exe"
      - "C:/Program Files/bin/cuda-memcheck"
      - "C:/Program Files/sbin/cuda-memcheck.com"
      - "C:/Program Files/sbin/cuda-memcheck.exe"
      - "C:/Program Files/sbin/cuda-memcheck"
      - "C:/Program Files/cuda-memcheck.com"
      - "C:/Program Files/cuda-memcheck.exe"
      - "C:/Program Files/cuda-memcheck"
      - "C:/Program Files (x86)/bin/cuda-memcheck.com"
      - "C:/Program Files (x86)/bin/cuda-memcheck.exe"
      - "C:/Program Files (x86)/bin/cuda-memcheck"
      - "C:/Program Files (x86)/sbin/cuda-memcheck.com"
      - "C:/Program Files (x86)/sbin/cuda-memcheck.exe"
      - "C:/Program Files (x86)/sbin/cuda-memcheck"
      - "C:/Program Files (x86)/cuda-memcheck.com"
      - "C:/Program Files (x86)/cuda-memcheck.exe"
      - "C:/Program Files (x86)/cuda-memcheck"
      - "C:/Program Files/CMake/bin/cuda-memcheck.com"
      - "C:/Program Files/CMake/bin/cuda-memcheck.exe"
      - "C:/Program Files/CMake/bin/cuda-memcheck"
      - "C:/Program Files/CMake/sbin/cuda-memcheck.com"
      - "C:/Program Files/CMake/sbin/cuda-memcheck.exe"
      - "C:/Program Files/CMake/sbin/cuda-memcheck"
      - "C:/Program Files/CMake/cuda-memcheck.com"
      - "C:/Program Files/CMake/cuda-memcheck.exe"
      - "C:/Program Files/CMake/cuda-memcheck"
      - "/REGISTRY-NOTFOUND/cuda-memcheck.com"
      - "/REGISTRY-NOTFOUND/cuda-memcheck.exe"
      - "/REGISTRY-NOTFOUND/cuda-memcheck"
      - "C:/Program Files/Java/jdk-21/bin/compute-sanitizer.com"
      - "C:/Program Files/Java/jdk-21/bin/compute-sanitizer.exe"
      - "C:/Program Files/Java/jdk-21/bin/compute-sanitizer"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/compute-sanitizer.com"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/compute-sanitizer.exe"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/compute-sanitizer"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/compute-sanitizer.com"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/compute-sanitizer.exe"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/compute-sanitizer"
      - "C:/Windows/System32/compute-sanitizer.com"
      - "C:/Windows/System32/compute-sanitizer.exe"
      - "C:/Windows/System32/compute-sanitizer"
      - "C:/Windows/compute-sanitizer.com"
      - "C:/Windows/compute-sanitizer.exe"
      - "C:/Windows/compute-sanitizer"
      - "C:/Windows/System32/wbem/compute-sanitizer.com"
      - "C:/Windows/System32/wbem/compute-sanitizer.exe"
      - "C:/Windows/System32/wbem/compute-sanitizer"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/compute-sanitizer.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/compute-sanitizer.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/compute-sanitizer"
      - "C:/Windows/System32/OpenSSH/compute-sanitizer.com"
      - "C:/Windows/System32/OpenSSH/compute-sanitizer.exe"
      - "C:/Windows/System32/OpenSSH/compute-sanitizer"
      - "C:/ProgramData/chocolatey/bin/compute-sanitizer.com"
      - "C:/ProgramData/chocolatey/bin/compute-sanitizer.exe"
      - "C:/ProgramData/chocolatey/bin/compute-sanitizer"
      - "C:/Program Files/Microsoft VS Code/bin/compute-sanitizer.com"
      - "C:/Program Files/Microsoft VS Code/bin/compute-sanitizer.exe"
      - "C:/Program Files/Microsoft VS Code/bin/compute-sanitizer"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/compute-sanitizer.com"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/compute-sanitizer.exe"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/compute-sanitizer"
      - "C:/Portable Applications/compute-sanitizer.com"
      - "C:/Portable Applications/compute-sanitizer.exe"
      - "C:/Portable Applications/compute-sanitizer"
      - "C:/Portable Applications/yt-dlp/compute-sanitizer.com"
      - "C:/Portable Applications/yt-dlp/compute-sanitizer.exe"
      - "C:/Portable Applications/yt-dlp/compute-sanitizer"
      - "C:/Program Files/Calibre2/compute-sanitizer.com"
      - "C:/Program Files/Calibre2/compute-sanitizer.exe"
      - "C:/Program Files/Calibre2/compute-sanitizer"
      - "C:/Program Files/Git/cmd/compute-sanitizer.com"
      - "C:/Program Files/Git/cmd/compute-sanitizer.exe"
      - "C:/Program Files/Git/cmd/compute-sanitizer"
      - "C:/Program Files/Docker/Docker/resources/bin/compute-sanitizer.com"
      - "C:/Program Files/Docker/Docker/resources/bin/compute-sanitizer.exe"
      - "C:/Program Files/Docker/Docker/resources/bin/compute-sanitizer"
      - "C:/Portable Applications/zig/compute-sanitizer.com"
      - "C:/Portable Applications/zig/compute-sanitizer.exe"
      - "C:/Portable Applications/zig/compute-sanitizer"
      - "C:/Program Files/nodejs/compute-sanitizer.com"
      - "C:/Program Files/nodejs/compute-sanitizer.exe"
      - "C:/Program Files/nodejs/compute-sanitizer"
      - "C:/Program Files/dotnet/compute-sanitizer.com"
      - "C:/Program Files/dotnet/compute-sanitizer.exe"
      - "C:/Program Files/dotnet/compute-sanitizer"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/compute-sanitizer.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/compute-sanitizer.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/compute-sanitizer"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/compute-sanitizer.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/compute-sanitizer.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/compute-sanitizer"
      - "C:/Users/<USER>/AppData/Local/pnpm/compute-sanitizer.com"
      - "C:/Users/<USER>/AppData/Local/pnpm/compute-sanitizer.exe"
      - "C:/Users/<USER>/AppData/Local/pnpm/compute-sanitizer"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/compute-sanitizer.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/compute-sanitizer.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/compute-sanitizer"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/compute-sanitizer.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/compute-sanitizer.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/compute-sanitizer"
      - "C:/Users/<USER>/.cargo/bin/compute-sanitizer.com"
      - "C:/Users/<USER>/.cargo/bin/compute-sanitizer.exe"
      - "C:/Users/<USER>/.cargo/bin/compute-sanitizer"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/compute-sanitizer.com"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/compute-sanitizer.exe"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/compute-sanitizer"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/compute-sanitizer.com"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/compute-sanitizer.exe"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/compute-sanitizer"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/compute-sanitizer.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/compute-sanitizer.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/compute-sanitizer"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/compute-sanitizer.com"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/compute-sanitizer.exe"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/compute-sanitizer"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/compute-sanitizer.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/compute-sanitizer.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/compute-sanitizer"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/compute-sanitizer.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/compute-sanitizer.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/compute-sanitizer"
      - "C:/Users/<USER>/.deno/bin/compute-sanitizer.com"
      - "C:/Users/<USER>/.deno/bin/compute-sanitizer.exe"
      - "C:/Users/<USER>/.deno/bin/compute-sanitizer"
      - "C:/Users/<USER>/AppData/Roaming/npm/compute-sanitizer.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/compute-sanitizer.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/compute-sanitizer"
      - "C:/Program Files/bin/compute-sanitizer.com"
      - "C:/Program Files/bin/compute-sanitizer.exe"
      - "C:/Program Files/bin/compute-sanitizer"
      - "C:/Program Files/sbin/compute-sanitizer.com"
      - "C:/Program Files/sbin/compute-sanitizer.exe"
      - "C:/Program Files/sbin/compute-sanitizer"
      - "C:/Program Files/compute-sanitizer.com"
      - "C:/Program Files/compute-sanitizer.exe"
      - "C:/Program Files/compute-sanitizer"
      - "C:/Program Files (x86)/bin/compute-sanitizer.com"
      - "C:/Program Files (x86)/bin/compute-sanitizer.exe"
      - "C:/Program Files (x86)/bin/compute-sanitizer"
      - "C:/Program Files (x86)/sbin/compute-sanitizer.com"
      - "C:/Program Files (x86)/sbin/compute-sanitizer.exe"
      - "C:/Program Files (x86)/sbin/compute-sanitizer"
      - "C:/Program Files (x86)/compute-sanitizer.com"
      - "C:/Program Files (x86)/compute-sanitizer.exe"
      - "C:/Program Files (x86)/compute-sanitizer"
      - "C:/Program Files/CMake/bin/compute-sanitizer.com"
      - "C:/Program Files/CMake/bin/compute-sanitizer.exe"
      - "C:/Program Files/CMake/bin/compute-sanitizer"
      - "C:/Program Files/CMake/sbin/compute-sanitizer.com"
      - "C:/Program Files/CMake/sbin/compute-sanitizer.exe"
      - "C:/Program Files/CMake/sbin/compute-sanitizer"
      - "C:/Program Files/CMake/compute-sanitizer.com"
      - "C:/Program Files/CMake/compute-sanitizer.exe"
      - "C:/Program Files/CMake/compute-sanitizer"
      - "/REGISTRY-NOTFOUND/compute-sanitizer.com"
      - "/REGISTRY-NOTFOUND/compute-sanitizer.exe"
      - "/REGISTRY-NOTFOUND/compute-sanitizer"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\Program Files\\Java\\jdk-21\\bin"
        - "C:\\Program Files\\Common Files\\Oracle\\Java\\javapath"
        - "C:\\Program Files\\Microsoft\\jdk-*********-hotspot\\bin"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\ProgramData\\chocolatey\\bin"
        - "C:\\Program Files\\Microsoft VS Code\\bin"
        - "C:\\Program Files\\Open Steno Project\\Plover 5.0.0.dev1"
        - "C:\\Portable Applications"
        - "C:\\Portable Applications\\yt-dlp"
        - "C:\\Program Files\\Calibre2\\"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "C:\\Portable Applications\\zig"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\pnpm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\"
        - "C:\\Users\\<USER>\\.cargo\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\platform-tools"
        - "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\tools"
        - "C:\\Users\\<USER>\\AppData\\Local\\pnpm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Ollama"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Users\\<USER>\\.deno\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
      CMAKE_INSTALL_PREFIX: "C:/Program Files (x86)/."
      CMAKE_SYSTEM_PREFIX_PATH:
        - "C:/Program Files"
        - "C:/Program Files (x86)"
        - "C:/Program Files/CMake"
        - "C:/Program Files (x86)/."
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CTest.cmake:196 (find_program)"
      - "CMakeLists.txt:6 (include)"
    mode: "program"
    variable: "COVERAGE_COMMAND"
    description: "Path to the coverage program that CTest uses for performing coverage inspection"
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "gcov"
    candidate_directories:
      - "C:/Program Files/Java/jdk-21/bin/"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/ProgramData/chocolatey/bin/"
      - "C:/Program Files/Microsoft VS Code/bin/"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/"
      - "C:/Portable Applications/"
      - "C:/Portable Applications/yt-dlp/"
      - "C:/Program Files/Calibre2/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/Docker/Docker/resources/bin/"
      - "C:/Portable Applications/zig/"
      - "C:/Program Files/nodejs/"
      - "C:/Program Files/dotnet/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/"
      - "C:/Users/<USER>/AppData/Local/pnpm/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/"
      - "C:/Users/<USER>/.cargo/bin/"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Users/<USER>/.deno/bin/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "C:/Program Files/bin/"
      - "C:/Program Files/sbin/"
      - "C:/Program Files/"
      - "C:/Program Files (x86)/bin/"
      - "C:/Program Files (x86)/sbin/"
      - "C:/Program Files (x86)/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Program Files/CMake/sbin/"
      - "C:/Program Files/CMake/"
    searched_directories:
      - "C:/Program Files/Java/jdk-21/bin/gcov.com"
      - "C:/Program Files/Java/jdk-21/bin/gcov.exe"
      - "C:/Program Files/Java/jdk-21/bin/gcov"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/gcov.com"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/gcov.exe"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/gcov"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/gcov.com"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/gcov.exe"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/gcov"
      - "C:/Windows/System32/gcov.com"
      - "C:/Windows/System32/gcov.exe"
      - "C:/Windows/System32/gcov"
      - "C:/Windows/gcov.com"
      - "C:/Windows/gcov.exe"
      - "C:/Windows/gcov"
      - "C:/Windows/System32/wbem/gcov.com"
      - "C:/Windows/System32/wbem/gcov.exe"
      - "C:/Windows/System32/wbem/gcov"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcov.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcov.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcov"
      - "C:/Windows/System32/OpenSSH/gcov.com"
      - "C:/Windows/System32/OpenSSH/gcov.exe"
      - "C:/Windows/System32/OpenSSH/gcov"
      - "C:/ProgramData/chocolatey/bin/gcov.com"
      - "C:/ProgramData/chocolatey/bin/gcov.exe"
      - "C:/ProgramData/chocolatey/bin/gcov"
      - "C:/Program Files/Microsoft VS Code/bin/gcov.com"
      - "C:/Program Files/Microsoft VS Code/bin/gcov.exe"
      - "C:/Program Files/Microsoft VS Code/bin/gcov"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/gcov.com"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/gcov.exe"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/gcov"
      - "C:/Portable Applications/gcov.com"
      - "C:/Portable Applications/gcov.exe"
      - "C:/Portable Applications/gcov"
      - "C:/Portable Applications/yt-dlp/gcov.com"
      - "C:/Portable Applications/yt-dlp/gcov.exe"
      - "C:/Portable Applications/yt-dlp/gcov"
      - "C:/Program Files/Calibre2/gcov.com"
      - "C:/Program Files/Calibre2/gcov.exe"
      - "C:/Program Files/Calibre2/gcov"
      - "C:/Program Files/Git/cmd/gcov.com"
      - "C:/Program Files/Git/cmd/gcov.exe"
      - "C:/Program Files/Git/cmd/gcov"
      - "C:/Program Files/Docker/Docker/resources/bin/gcov.com"
      - "C:/Program Files/Docker/Docker/resources/bin/gcov.exe"
      - "C:/Program Files/Docker/Docker/resources/bin/gcov"
      - "C:/Portable Applications/zig/gcov.com"
      - "C:/Portable Applications/zig/gcov.exe"
      - "C:/Portable Applications/zig/gcov"
      - "C:/Program Files/nodejs/gcov.com"
      - "C:/Program Files/nodejs/gcov.exe"
      - "C:/Program Files/nodejs/gcov"
      - "C:/Program Files/dotnet/gcov.com"
      - "C:/Program Files/dotnet/gcov.exe"
      - "C:/Program Files/dotnet/gcov"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/gcov.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/gcov.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/gcov"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/gcov.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/gcov.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/gcov"
      - "C:/Users/<USER>/AppData/Local/pnpm/gcov.com"
      - "C:/Users/<USER>/AppData/Local/pnpm/gcov.exe"
      - "C:/Users/<USER>/AppData/Local/pnpm/gcov"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/gcov.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/gcov.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/gcov"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/gcov.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/gcov.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/gcov"
      - "C:/Users/<USER>/.cargo/bin/gcov.com"
      - "C:/Users/<USER>/.cargo/bin/gcov.exe"
      - "C:/Users/<USER>/.cargo/bin/gcov"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/gcov.com"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/gcov.exe"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/gcov"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/gcov.com"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/gcov.exe"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/gcov"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcov.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcov.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcov"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/gcov.com"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/gcov.exe"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/gcov"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/gcov.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/gcov.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/gcov"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/gcov.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/gcov.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/gcov"
      - "C:/Users/<USER>/.deno/bin/gcov.com"
      - "C:/Users/<USER>/.deno/bin/gcov.exe"
      - "C:/Users/<USER>/.deno/bin/gcov"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcov.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcov.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcov"
      - "C:/Program Files/bin/gcov.com"
      - "C:/Program Files/bin/gcov.exe"
      - "C:/Program Files/bin/gcov"
      - "C:/Program Files/sbin/gcov.com"
      - "C:/Program Files/sbin/gcov.exe"
      - "C:/Program Files/sbin/gcov"
      - "C:/Program Files/gcov.com"
      - "C:/Program Files/gcov.exe"
      - "C:/Program Files/gcov"
      - "C:/Program Files (x86)/bin/gcov.com"
      - "C:/Program Files (x86)/bin/gcov.exe"
      - "C:/Program Files (x86)/bin/gcov"
      - "C:/Program Files (x86)/sbin/gcov.com"
      - "C:/Program Files (x86)/sbin/gcov.exe"
      - "C:/Program Files (x86)/sbin/gcov"
      - "C:/Program Files (x86)/gcov.com"
      - "C:/Program Files (x86)/gcov.exe"
      - "C:/Program Files (x86)/gcov"
      - "C:/Program Files/CMake/bin/gcov.com"
      - "C:/Program Files/CMake/bin/gcov.exe"
      - "C:/Program Files/CMake/bin/gcov"
      - "C:/Program Files/CMake/sbin/gcov.com"
      - "C:/Program Files/CMake/sbin/gcov.exe"
      - "C:/Program Files/CMake/sbin/gcov"
      - "C:/Program Files/CMake/gcov.com"
      - "C:/Program Files/CMake/gcov.exe"
      - "C:/Program Files/CMake/gcov"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\Program Files\\Java\\jdk-21\\bin"
        - "C:\\Program Files\\Common Files\\Oracle\\Java\\javapath"
        - "C:\\Program Files\\Microsoft\\jdk-*********-hotspot\\bin"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\ProgramData\\chocolatey\\bin"
        - "C:\\Program Files\\Microsoft VS Code\\bin"
        - "C:\\Program Files\\Open Steno Project\\Plover 5.0.0.dev1"
        - "C:\\Portable Applications"
        - "C:\\Portable Applications\\yt-dlp"
        - "C:\\Program Files\\Calibre2\\"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "C:\\Portable Applications\\zig"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\pnpm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\"
        - "C:\\Users\\<USER>\\.cargo\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\platform-tools"
        - "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\tools"
        - "C:\\Users\\<USER>\\AppData\\Local\\pnpm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Ollama"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Users\\<USER>\\.deno\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
      CMAKE_INSTALL_PREFIX: "C:/Program Files (x86)/."
      CMAKE_SYSTEM_PREFIX_PATH:
        - "C:/Program Files"
        - "C:/Program Files (x86)"
        - "C:/Program Files/CMake"
        - "C:/Program Files (x86)/."
...

---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineSystem.cmake:212 (message)"
      - "CMakeLists.txt:2 (project)"
    message: |
      The system is: Windows - 10.0.26100 - AMD64
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:462 (find_file)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:500 (CMAKE_DETERMINE_COMPILER_ID_WRITE)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:8 (CMAKE_DETERMINE_COMPILER_ID_BUILD)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:122 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    mode: "file"
    variable: "src_in"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "CMakeCCompilerId.c.in"
    candidate_directories:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/"
    found: "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeCCompilerId.c.in"
    search_context:
      ENV{PATH}:
        - "C:\\Program Files\\Java\\jdk-21\\bin"
        - "C:\\Program Files\\Common Files\\Oracle\\Java\\javapath"
        - "C:\\Program Files\\Microsoft\\jdk-*********-hotspot\\bin"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\ProgramData\\chocolatey\\bin"
        - "C:\\Program Files\\Microsoft VS Code\\bin"
        - "C:\\Program Files\\Open Steno Project\\Plover 5.0.0.dev1"
        - "C:\\Portable Applications"
        - "C:\\Portable Applications\\yt-dlp"
        - "C:\\Program Files\\Calibre2\\"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "C:\\Portable Applications\\zig"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\pnpm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\"
        - "C:\\Users\\<USER>\\.cargo\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\platform-tools"
        - "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\tools"
        - "C:\\Users\\<USER>\\AppData\\Local\\pnpm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Ollama"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Users\\<USER>\\.deno\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:122 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      MSBuild version 17.13.19+0d9f5a35a for .NET Framework
      Build started 7/10/2025 5:48:27 PM.
      
      Project "C:\\Users\\<USER>\\_Items\\Dev\\GitHub\\scale\\aider-quality-cpp\\build\\CMakeFiles\\4.1.0-rc1\\CompilerIdC\\CompilerIdC.vcxproj" on node 1 (default targets).
      PrepareForBuild:
        Creating directory "Debug\\".
        Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
        Creating directory "Debug\\CompilerIdC.tlog\\".
      InitializeBuildStatus:
        Creating "Debug\\CompilerIdC.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
        Touching "Debug\\CompilerIdC.tlog\\unsuccessfulbuild".
      ClCompile:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\CL.exe /c /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc143.pdb" /external:W0 /Gd /TC /FC /errorReport:queue CMakeCCompilerId.c
        CMakeCCompilerId.c
      Link:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdC.exe" /INCREMENTAL:NO /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdC.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdC.lib" /MACHINE:X64 Debug\\CMakeCCompilerId.obj
        CompilerIdC.vcxproj -> C:\\Users\\<USER>\\_Items\\Dev\\GitHub\\scale\\aider-quality-cpp\\build\\CMakeFiles\\4.1.0-rc1\\CompilerIdC\\CompilerIdC.exe
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_C_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_C_COMPILER=C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\Hostx64\\x64\\cl.exe
      FinalizeBuildStatus:
        Deleting file "Debug\\CompilerIdC.tlog\\unsuccessfulbuild".
        Touching "Debug\\CompilerIdC.tlog\\CompilerIdC.lastbuildstate".
      Done Building Project "C:\\Users\\<USER>\\_Items\\Dev\\GitHub\\scale\\aider-quality-cpp\\build\\CMakeFiles\\4.1.0-rc1\\CompilerIdC\\CompilerIdC.vcxproj" (default targets).
      
      Build succeeded.
          0 Warning(s)
          0 Error(s)
      
      Time Elapsed 00:00:00.75
      
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CompilerIdC.exe"
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CompilerIdC.vcxproj"
      
      The C compiler identification is MSVC, found in:
        C:/Users/<USER>/_Items/Dev/GitHub/scale/aider-quality-cpp/build/CMakeFiles/4.1.0-rc1/CompilerIdC/CompilerIdC.exe
      
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:37 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:65 (__resolve_tool_path)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:103 (__resolve_linker_path)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "_CMAKE_TOOL_WITH_PATH"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "link"
    candidate_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/"
      - "C:/Program Files/Java/jdk-21/bin/"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/ProgramData/chocolatey/bin/"
      - "C:/Program Files/Microsoft VS Code/bin/"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/"
      - "C:/Portable Applications/"
      - "C:/Portable Applications/yt-dlp/"
      - "C:/Program Files/Calibre2/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/Docker/Docker/resources/bin/"
      - "C:/Portable Applications/zig/"
      - "C:/Program Files/nodejs/"
      - "C:/Program Files/dotnet/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/"
      - "C:/Users/<USER>/AppData/Local/pnpm/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/"
      - "C:/Users/<USER>/.cargo/bin/"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Users/<USER>/.deno/bin/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
    searched_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/link.com"
    found: "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/link.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Program Files\\Java\\jdk-21\\bin"
        - "C:\\Program Files\\Common Files\\Oracle\\Java\\javapath"
        - "C:\\Program Files\\Microsoft\\jdk-*********-hotspot\\bin"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\ProgramData\\chocolatey\\bin"
        - "C:\\Program Files\\Microsoft VS Code\\bin"
        - "C:\\Program Files\\Open Steno Project\\Plover 5.0.0.dev1"
        - "C:\\Portable Applications"
        - "C:\\Portable Applications\\yt-dlp"
        - "C:\\Program Files\\Calibre2\\"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "C:\\Portable Applications\\zig"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\pnpm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\"
        - "C:\\Users\\<USER>\\.cargo\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\platform-tools"
        - "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\tools"
        - "C:\\Users\\<USER>\\AppData\\Local\\pnpm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Ollama"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Users\\<USER>\\.deno\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:37 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:65 (__resolve_tool_path)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:104 (__resolve_linker_path)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "_CMAKE_TOOL_WITH_PATH"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "lld-link"
    candidate_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/"
      - "C:/Program Files/Java/jdk-21/bin/"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/ProgramData/chocolatey/bin/"
      - "C:/Program Files/Microsoft VS Code/bin/"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/"
      - "C:/Portable Applications/"
      - "C:/Portable Applications/yt-dlp/"
      - "C:/Program Files/Calibre2/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/Docker/Docker/resources/bin/"
      - "C:/Portable Applications/zig/"
      - "C:/Program Files/nodejs/"
      - "C:/Program Files/dotnet/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/"
      - "C:/Users/<USER>/AppData/Local/pnpm/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/"
      - "C:/Users/<USER>/.cargo/bin/"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Users/<USER>/.deno/bin/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
    searched_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/lld-link.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/lld-link.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/lld-link"
      - "C:/Program Files/Java/jdk-21/bin/lld-link.com"
      - "C:/Program Files/Java/jdk-21/bin/lld-link.exe"
      - "C:/Program Files/Java/jdk-21/bin/lld-link"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/lld-link.com"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/lld-link.exe"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/lld-link"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/lld-link.com"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/lld-link.exe"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/lld-link"
      - "C:/Windows/System32/lld-link.com"
      - "C:/Windows/System32/lld-link.exe"
      - "C:/Windows/System32/lld-link"
      - "C:/Windows/lld-link.com"
      - "C:/Windows/lld-link.exe"
      - "C:/Windows/lld-link"
      - "C:/Windows/System32/wbem/lld-link.com"
      - "C:/Windows/System32/wbem/lld-link.exe"
      - "C:/Windows/System32/wbem/lld-link"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/lld-link.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/lld-link.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/lld-link"
      - "C:/Windows/System32/OpenSSH/lld-link.com"
      - "C:/Windows/System32/OpenSSH/lld-link.exe"
      - "C:/Windows/System32/OpenSSH/lld-link"
      - "C:/ProgramData/chocolatey/bin/lld-link.com"
      - "C:/ProgramData/chocolatey/bin/lld-link.exe"
      - "C:/ProgramData/chocolatey/bin/lld-link"
      - "C:/Program Files/Microsoft VS Code/bin/lld-link.com"
      - "C:/Program Files/Microsoft VS Code/bin/lld-link.exe"
      - "C:/Program Files/Microsoft VS Code/bin/lld-link"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/lld-link.com"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/lld-link.exe"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/lld-link"
      - "C:/Portable Applications/lld-link.com"
      - "C:/Portable Applications/lld-link.exe"
      - "C:/Portable Applications/lld-link"
      - "C:/Portable Applications/yt-dlp/lld-link.com"
      - "C:/Portable Applications/yt-dlp/lld-link.exe"
      - "C:/Portable Applications/yt-dlp/lld-link"
      - "C:/Program Files/Calibre2/lld-link.com"
      - "C:/Program Files/Calibre2/lld-link.exe"
      - "C:/Program Files/Calibre2/lld-link"
      - "C:/Program Files/Git/cmd/lld-link.com"
      - "C:/Program Files/Git/cmd/lld-link.exe"
      - "C:/Program Files/Git/cmd/lld-link"
      - "C:/Program Files/Docker/Docker/resources/bin/lld-link.com"
      - "C:/Program Files/Docker/Docker/resources/bin/lld-link.exe"
      - "C:/Program Files/Docker/Docker/resources/bin/lld-link"
      - "C:/Portable Applications/zig/lld-link.com"
      - "C:/Portable Applications/zig/lld-link.exe"
      - "C:/Portable Applications/zig/lld-link"
      - "C:/Program Files/nodejs/lld-link.com"
      - "C:/Program Files/nodejs/lld-link.exe"
      - "C:/Program Files/nodejs/lld-link"
      - "C:/Program Files/dotnet/lld-link.com"
      - "C:/Program Files/dotnet/lld-link.exe"
      - "C:/Program Files/dotnet/lld-link"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/lld-link"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/lld-link"
      - "C:/Users/<USER>/AppData/Local/pnpm/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/pnpm/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/pnpm/lld-link"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/lld-link"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/lld-link"
      - "C:/Users/<USER>/.cargo/bin/lld-link.com"
      - "C:/Users/<USER>/.cargo/bin/lld-link.exe"
      - "C:/Users/<USER>/.cargo/bin/lld-link"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/lld-link"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/lld-link"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/lld-link"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/lld-link"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/lld-link"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/lld-link"
      - "C:/Users/<USER>/.deno/bin/lld-link.com"
      - "C:/Users/<USER>/.deno/bin/lld-link.exe"
      - "C:/Users/<USER>/.deno/bin/lld-link"
      - "C:/Users/<USER>/AppData/Roaming/npm/lld-link.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/lld-link.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/lld-link"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\Program Files\\Java\\jdk-21\\bin"
        - "C:\\Program Files\\Common Files\\Oracle\\Java\\javapath"
        - "C:\\Program Files\\Microsoft\\jdk-*********-hotspot\\bin"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\ProgramData\\chocolatey\\bin"
        - "C:\\Program Files\\Microsoft VS Code\\bin"
        - "C:\\Program Files\\Open Steno Project\\Plover 5.0.0.dev1"
        - "C:\\Portable Applications"
        - "C:\\Portable Applications\\yt-dlp"
        - "C:\\Program Files\\Calibre2\\"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "C:\\Portable Applications\\zig"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\pnpm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\"
        - "C:\\Users\\<USER>\\.cargo\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\platform-tools"
        - "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\tools"
        - "C:\\Users\\<USER>\\AppData\\Local\\pnpm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Ollama"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Users\\<USER>\\.deno\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_LINKER"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "link"
    candidate_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/"
      - "C:/Program Files/Java/jdk-21/bin/"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/ProgramData/chocolatey/bin/"
      - "C:/Program Files/Microsoft VS Code/bin/"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/"
      - "C:/Portable Applications/"
      - "C:/Portable Applications/yt-dlp/"
      - "C:/Program Files/Calibre2/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/Docker/Docker/resources/bin/"
      - "C:/Portable Applications/zig/"
      - "C:/Program Files/nodejs/"
      - "C:/Program Files/dotnet/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/"
      - "C:/Users/<USER>/AppData/Local/pnpm/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/"
      - "C:/Users/<USER>/.cargo/bin/"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Users/<USER>/.deno/bin/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
    searched_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/link.com"
    found: "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/link.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Program Files\\Java\\jdk-21\\bin"
        - "C:\\Program Files\\Common Files\\Oracle\\Java\\javapath"
        - "C:\\Program Files\\Microsoft\\jdk-*********-hotspot\\bin"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\ProgramData\\chocolatey\\bin"
        - "C:\\Program Files\\Microsoft VS Code\\bin"
        - "C:\\Program Files\\Open Steno Project\\Plover 5.0.0.dev1"
        - "C:\\Portable Applications"
        - "C:\\Portable Applications\\yt-dlp"
        - "C:\\Program Files\\Calibre2\\"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "C:\\Portable Applications\\zig"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\pnpm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\"
        - "C:\\Users\\<USER>\\.cargo\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\platform-tools"
        - "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\tools"
        - "C:\\Users\\<USER>\\AppData\\Local\\pnpm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Ollama"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Users\\<USER>\\.deno\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_MT"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "mt"
    candidate_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/"
      - "C:/Program Files/Java/jdk-21/bin/"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/ProgramData/chocolatey/bin/"
      - "C:/Program Files/Microsoft VS Code/bin/"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/"
      - "C:/Portable Applications/"
      - "C:/Portable Applications/yt-dlp/"
      - "C:/Program Files/Calibre2/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/Docker/Docker/resources/bin/"
      - "C:/Portable Applications/zig/"
      - "C:/Program Files/nodejs/"
      - "C:/Program Files/dotnet/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/"
      - "C:/Users/<USER>/AppData/Local/pnpm/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/"
      - "C:/Users/<USER>/.cargo/bin/"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Users/<USER>/.deno/bin/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
    searched_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/mt.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/mt.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/mt"
      - "C:/Program Files/Java/jdk-21/bin/mt.com"
      - "C:/Program Files/Java/jdk-21/bin/mt.exe"
      - "C:/Program Files/Java/jdk-21/bin/mt"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/mt.com"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/mt.exe"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/mt"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/mt.com"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/mt.exe"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/mt"
      - "C:/Windows/System32/mt.com"
      - "C:/Windows/System32/mt.exe"
      - "C:/Windows/System32/mt"
      - "C:/Windows/mt.com"
      - "C:/Windows/mt.exe"
      - "C:/Windows/mt"
      - "C:/Windows/System32/wbem/mt.com"
      - "C:/Windows/System32/wbem/mt.exe"
      - "C:/Windows/System32/wbem/mt"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/mt.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/mt.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/mt"
      - "C:/Windows/System32/OpenSSH/mt.com"
      - "C:/Windows/System32/OpenSSH/mt.exe"
      - "C:/Windows/System32/OpenSSH/mt"
      - "C:/ProgramData/chocolatey/bin/mt.com"
      - "C:/ProgramData/chocolatey/bin/mt.exe"
      - "C:/ProgramData/chocolatey/bin/mt"
      - "C:/Program Files/Microsoft VS Code/bin/mt.com"
      - "C:/Program Files/Microsoft VS Code/bin/mt.exe"
      - "C:/Program Files/Microsoft VS Code/bin/mt"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/mt.com"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/mt.exe"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/mt"
      - "C:/Portable Applications/mt.com"
      - "C:/Portable Applications/mt.exe"
      - "C:/Portable Applications/mt"
      - "C:/Portable Applications/yt-dlp/mt.com"
      - "C:/Portable Applications/yt-dlp/mt.exe"
      - "C:/Portable Applications/yt-dlp/mt"
      - "C:/Program Files/Calibre2/mt.com"
      - "C:/Program Files/Calibre2/mt.exe"
      - "C:/Program Files/Calibre2/mt"
      - "C:/Program Files/Git/cmd/mt.com"
      - "C:/Program Files/Git/cmd/mt.exe"
      - "C:/Program Files/Git/cmd/mt"
      - "C:/Program Files/Docker/Docker/resources/bin/mt.com"
      - "C:/Program Files/Docker/Docker/resources/bin/mt.exe"
      - "C:/Program Files/Docker/Docker/resources/bin/mt"
      - "C:/Portable Applications/zig/mt.com"
      - "C:/Portable Applications/zig/mt.exe"
      - "C:/Portable Applications/zig/mt"
      - "C:/Program Files/nodejs/mt.com"
      - "C:/Program Files/nodejs/mt.exe"
      - "C:/Program Files/nodejs/mt"
      - "C:/Program Files/dotnet/mt.com"
      - "C:/Program Files/dotnet/mt.exe"
      - "C:/Program Files/dotnet/mt"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/mt.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/mt.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/mt"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/mt.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/mt.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/mt"
      - "C:/Users/<USER>/AppData/Local/pnpm/mt.com"
      - "C:/Users/<USER>/AppData/Local/pnpm/mt.exe"
      - "C:/Users/<USER>/AppData/Local/pnpm/mt"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/mt.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/mt.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/mt"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/mt.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/mt.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/mt"
      - "C:/Users/<USER>/.cargo/bin/mt.com"
      - "C:/Users/<USER>/.cargo/bin/mt.exe"
      - "C:/Users/<USER>/.cargo/bin/mt"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/mt.com"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/mt.exe"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/mt"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/mt.com"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/mt.exe"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/mt"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/mt.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/mt.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/mt"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/mt.com"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/mt.exe"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/mt"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/mt.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/mt.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/mt"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/mt.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/mt.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/mt"
      - "C:/Users/<USER>/.deno/bin/mt.com"
      - "C:/Users/<USER>/.deno/bin/mt.exe"
      - "C:/Users/<USER>/.deno/bin/mt"
      - "C:/Users/<USER>/AppData/Roaming/npm/mt.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/mt.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/mt"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\Program Files\\Java\\jdk-21\\bin"
        - "C:\\Program Files\\Common Files\\Oracle\\Java\\javapath"
        - "C:\\Program Files\\Microsoft\\jdk-*********-hotspot\\bin"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\ProgramData\\chocolatey\\bin"
        - "C:\\Program Files\\Microsoft VS Code\\bin"
        - "C:\\Program Files\\Open Steno Project\\Plover 5.0.0.dev1"
        - "C:\\Portable Applications"
        - "C:\\Portable Applications\\yt-dlp"
        - "C:\\Program Files\\Calibre2\\"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "C:\\Portable Applications\\zig"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\pnpm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\"
        - "C:\\Users\\<USER>\\.cargo\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\platform-tools"
        - "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\tools"
        - "C:\\Users\\<USER>\\AppData\\Local\\pnpm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Ollama"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Users\\<USER>\\.deno\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_AR"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "lib"
    candidate_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/"
      - "C:/Program Files/Java/jdk-21/bin/"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/ProgramData/chocolatey/bin/"
      - "C:/Program Files/Microsoft VS Code/bin/"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/"
      - "C:/Portable Applications/"
      - "C:/Portable Applications/yt-dlp/"
      - "C:/Program Files/Calibre2/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/Docker/Docker/resources/bin/"
      - "C:/Portable Applications/zig/"
      - "C:/Program Files/nodejs/"
      - "C:/Program Files/dotnet/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/"
      - "C:/Users/<USER>/AppData/Local/pnpm/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/"
      - "C:/Users/<USER>/.cargo/bin/"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Users/<USER>/.deno/bin/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
    searched_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/lib.com"
    found: "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/lib.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Program Files\\Java\\jdk-21\\bin"
        - "C:\\Program Files\\Common Files\\Oracle\\Java\\javapath"
        - "C:\\Program Files\\Microsoft\\jdk-*********-hotspot\\bin"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\ProgramData\\chocolatey\\bin"
        - "C:\\Program Files\\Microsoft VS Code\\bin"
        - "C:\\Program Files\\Open Steno Project\\Plover 5.0.0.dev1"
        - "C:\\Portable Applications"
        - "C:\\Portable Applications\\yt-dlp"
        - "C:\\Program Files\\Calibre2\\"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "C:\\Portable Applications\\zig"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\pnpm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\"
        - "C:\\Users\\<USER>\\.cargo\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\platform-tools"
        - "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\tools"
        - "C:\\Users\\<USER>\\AppData\\Local\\pnpm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Ollama"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Users\\<USER>\\.deno\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:462 (find_file)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:500 (CMAKE_DETERMINE_COMPILER_ID_WRITE)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:8 (CMAKE_DETERMINE_COMPILER_ID_BUILD)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    mode: "file"
    variable: "src_in"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "CMakeCXXCompilerId.cpp.in"
    candidate_directories:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/"
    found: "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeCXXCompilerId.cpp.in"
    search_context:
      ENV{PATH}:
        - "C:\\Program Files\\Java\\jdk-21\\bin"
        - "C:\\Program Files\\Common Files\\Oracle\\Java\\javapath"
        - "C:\\Program Files\\Microsoft\\jdk-*********-hotspot\\bin"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\ProgramData\\chocolatey\\bin"
        - "C:\\Program Files\\Microsoft VS Code\\bin"
        - "C:\\Program Files\\Open Steno Project\\Plover 5.0.0.dev1"
        - "C:\\Portable Applications"
        - "C:\\Portable Applications\\yt-dlp"
        - "C:\\Program Files\\Calibre2\\"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "C:\\Portable Applications\\zig"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\pnpm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\"
        - "C:\\Users\\<USER>\\.cargo\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\platform-tools"
        - "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\tools"
        - "C:\\Users\\<USER>\\AppData\\Local\\pnpm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Ollama"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Users\\<USER>\\.deno\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      MSBuild version 17.13.19+0d9f5a35a for .NET Framework
      Build started 7/10/2025 5:48:28 PM.
      
      Project "C:\\Users\\<USER>\\_Items\\Dev\\GitHub\\scale\\aider-quality-cpp\\build\\CMakeFiles\\4.1.0-rc1\\CompilerIdCXX\\CompilerIdCXX.vcxproj" on node 1 (default targets).
      PrepareForBuild:
        Creating directory "Debug\\".
        Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
        Creating directory "Debug\\CompilerIdCXX.tlog\\".
      InitializeBuildStatus:
        Creating "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
        Touching "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild".
      ClCompile:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\CL.exe /c /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc143.pdb" /external:W0 /Gd /TP /FC /errorReport:queue CMakeCXXCompilerId.cpp
        CMakeCXXCompilerId.cpp
      Link:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdCXX.exe" /INCREMENTAL:NO /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdCXX.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdCXX.lib" /MACHINE:X64 Debug\\CMakeCXXCompilerId.obj
        CompilerIdCXX.vcxproj -> C:\\Users\\<USER>\\_Items\\Dev\\GitHub\\scale\\aider-quality-cpp\\build\\CMakeFiles\\4.1.0-rc1\\CompilerIdCXX\\CompilerIdCXX.exe
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_CXX_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_CXX_COMPILER=C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\Hostx64\\x64\\cl.exe
      FinalizeBuildStatus:
        Deleting file "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild".
        Touching "Debug\\CompilerIdCXX.tlog\\CompilerIdCXX.lastbuildstate".
      Done Building Project "C:\\Users\\<USER>\\_Items\\Dev\\GitHub\\scale\\aider-quality-cpp\\build\\CMakeFiles\\4.1.0-rc1\\CompilerIdCXX\\CompilerIdCXX.vcxproj" (default targets).
      
      Build succeeded.
          0 Warning(s)
          0 Error(s)
      
      Time Elapsed 00:00:00.68
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.exe"
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.vcxproj"
      
      The CXX compiler identification is MSVC, found in:
        C:/Users/<USER>/_Items/Dev/GitHub/scale/aider-quality-cpp/build/CMakeFiles/4.1.0-rc1/CompilerIdCXX/CompilerIdCXX.exe
      
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:37 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:65 (__resolve_tool_path)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:104 (__resolve_linker_path)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:206 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "_CMAKE_TOOL_WITH_PATH"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "lld-link"
    candidate_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/"
      - "C:/Program Files/Java/jdk-21/bin/"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/ProgramData/chocolatey/bin/"
      - "C:/Program Files/Microsoft VS Code/bin/"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/"
      - "C:/Portable Applications/"
      - "C:/Portable Applications/yt-dlp/"
      - "C:/Program Files/Calibre2/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/Docker/Docker/resources/bin/"
      - "C:/Portable Applications/zig/"
      - "C:/Program Files/nodejs/"
      - "C:/Program Files/dotnet/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/"
      - "C:/Users/<USER>/AppData/Local/pnpm/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/"
      - "C:/Users/<USER>/.cargo/bin/"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Users/<USER>/.deno/bin/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
    searched_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/lld-link.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/lld-link.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/lld-link"
      - "C:/Program Files/Java/jdk-21/bin/lld-link.com"
      - "C:/Program Files/Java/jdk-21/bin/lld-link.exe"
      - "C:/Program Files/Java/jdk-21/bin/lld-link"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/lld-link.com"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/lld-link.exe"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/lld-link"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/lld-link.com"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/lld-link.exe"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/lld-link"
      - "C:/Windows/System32/lld-link.com"
      - "C:/Windows/System32/lld-link.exe"
      - "C:/Windows/System32/lld-link"
      - "C:/Windows/lld-link.com"
      - "C:/Windows/lld-link.exe"
      - "C:/Windows/lld-link"
      - "C:/Windows/System32/wbem/lld-link.com"
      - "C:/Windows/System32/wbem/lld-link.exe"
      - "C:/Windows/System32/wbem/lld-link"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/lld-link.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/lld-link.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/lld-link"
      - "C:/Windows/System32/OpenSSH/lld-link.com"
      - "C:/Windows/System32/OpenSSH/lld-link.exe"
      - "C:/Windows/System32/OpenSSH/lld-link"
      - "C:/ProgramData/chocolatey/bin/lld-link.com"
      - "C:/ProgramData/chocolatey/bin/lld-link.exe"
      - "C:/ProgramData/chocolatey/bin/lld-link"
      - "C:/Program Files/Microsoft VS Code/bin/lld-link.com"
      - "C:/Program Files/Microsoft VS Code/bin/lld-link.exe"
      - "C:/Program Files/Microsoft VS Code/bin/lld-link"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/lld-link.com"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/lld-link.exe"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/lld-link"
      - "C:/Portable Applications/lld-link.com"
      - "C:/Portable Applications/lld-link.exe"
      - "C:/Portable Applications/lld-link"
      - "C:/Portable Applications/yt-dlp/lld-link.com"
      - "C:/Portable Applications/yt-dlp/lld-link.exe"
      - "C:/Portable Applications/yt-dlp/lld-link"
      - "C:/Program Files/Calibre2/lld-link.com"
      - "C:/Program Files/Calibre2/lld-link.exe"
      - "C:/Program Files/Calibre2/lld-link"
      - "C:/Program Files/Git/cmd/lld-link.com"
      - "C:/Program Files/Git/cmd/lld-link.exe"
      - "C:/Program Files/Git/cmd/lld-link"
      - "C:/Program Files/Docker/Docker/resources/bin/lld-link.com"
      - "C:/Program Files/Docker/Docker/resources/bin/lld-link.exe"
      - "C:/Program Files/Docker/Docker/resources/bin/lld-link"
      - "C:/Portable Applications/zig/lld-link.com"
      - "C:/Portable Applications/zig/lld-link.exe"
      - "C:/Portable Applications/zig/lld-link"
      - "C:/Program Files/nodejs/lld-link.com"
      - "C:/Program Files/nodejs/lld-link.exe"
      - "C:/Program Files/nodejs/lld-link"
      - "C:/Program Files/dotnet/lld-link.com"
      - "C:/Program Files/dotnet/lld-link.exe"
      - "C:/Program Files/dotnet/lld-link"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/lld-link"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/lld-link"
      - "C:/Users/<USER>/AppData/Local/pnpm/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/pnpm/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/pnpm/lld-link"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/lld-link"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/lld-link"
      - "C:/Users/<USER>/.cargo/bin/lld-link.com"
      - "C:/Users/<USER>/.cargo/bin/lld-link.exe"
      - "C:/Users/<USER>/.cargo/bin/lld-link"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/lld-link"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/lld-link"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/lld-link"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/lld-link"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/lld-link"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/lld-link"
      - "C:/Users/<USER>/.deno/bin/lld-link.com"
      - "C:/Users/<USER>/.deno/bin/lld-link.exe"
      - "C:/Users/<USER>/.deno/bin/lld-link"
      - "C:/Users/<USER>/AppData/Roaming/npm/lld-link.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/lld-link.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/lld-link"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\Program Files\\Java\\jdk-21\\bin"
        - "C:\\Program Files\\Common Files\\Oracle\\Java\\javapath"
        - "C:\\Program Files\\Microsoft\\jdk-*********-hotspot\\bin"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\ProgramData\\chocolatey\\bin"
        - "C:\\Program Files\\Microsoft VS Code\\bin"
        - "C:\\Program Files\\Open Steno Project\\Plover 5.0.0.dev1"
        - "C:\\Portable Applications"
        - "C:\\Portable Applications\\yt-dlp"
        - "C:\\Program Files\\Calibre2\\"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "C:\\Portable Applications\\zig"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\pnpm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\"
        - "C:\\Users\\<USER>\\.cargo\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\platform-tools"
        - "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\tools"
        - "C:\\Users\\<USER>\\AppData\\Local\\pnpm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Ollama"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Users\\<USER>\\.deno\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineRCCompiler.cmake:40 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Platform/Windows-MSVC.cmake:573 (enable_language)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Platform/Windows-MSVC.cmake:546 (__windows_compiler_msvc_enable_rc)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Platform/Windows-MSVC-C.cmake:5 (__windows_compiler_msvc)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeCInformation.cmake:48 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_RC_COMPILER"
    description: "RC compiler"
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "rc"
    candidate_directories:
      - "C:/Program Files/Java/jdk-21/bin/"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/ProgramData/chocolatey/bin/"
      - "C:/Program Files/Microsoft VS Code/bin/"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/"
      - "C:/Portable Applications/"
      - "C:/Portable Applications/yt-dlp/"
      - "C:/Program Files/Calibre2/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/Docker/Docker/resources/bin/"
      - "C:/Portable Applications/zig/"
      - "C:/Program Files/nodejs/"
      - "C:/Program Files/dotnet/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/"
      - "C:/Users/<USER>/AppData/Local/pnpm/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/"
      - "C:/Users/<USER>/.cargo/bin/"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Users/<USER>/.deno/bin/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "C:/Program Files/bin/"
      - "C:/Program Files/sbin/"
      - "C:/Program Files/"
      - "C:/Program Files (x86)/bin/"
      - "C:/Program Files (x86)/sbin/"
      - "C:/Program Files (x86)/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Program Files/CMake/sbin/"
      - "C:/Program Files/CMake/"
    searched_directories:
      - "C:/Program Files/Java/jdk-21/bin/rc.com"
      - "C:/Program Files/Java/jdk-21/bin/rc.exe"
      - "C:/Program Files/Java/jdk-21/bin/rc"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/rc.com"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/rc.exe"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/rc"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/rc.com"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/rc.exe"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/rc"
      - "C:/Windows/System32/rc.com"
      - "C:/Windows/System32/rc.exe"
      - "C:/Windows/System32/rc"
      - "C:/Windows/rc.com"
      - "C:/Windows/rc.exe"
      - "C:/Windows/rc"
      - "C:/Windows/System32/wbem/rc.com"
      - "C:/Windows/System32/wbem/rc.exe"
      - "C:/Windows/System32/wbem/rc"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/rc.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/rc.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/rc"
      - "C:/Windows/System32/OpenSSH/rc.com"
      - "C:/Windows/System32/OpenSSH/rc.exe"
      - "C:/Windows/System32/OpenSSH/rc"
      - "C:/ProgramData/chocolatey/bin/rc.com"
      - "C:/ProgramData/chocolatey/bin/rc.exe"
      - "C:/ProgramData/chocolatey/bin/rc"
      - "C:/Program Files/Microsoft VS Code/bin/rc.com"
      - "C:/Program Files/Microsoft VS Code/bin/rc.exe"
      - "C:/Program Files/Microsoft VS Code/bin/rc"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/rc.com"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/rc.exe"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/rc"
      - "C:/Portable Applications/rc.com"
      - "C:/Portable Applications/rc.exe"
      - "C:/Portable Applications/rc"
      - "C:/Portable Applications/yt-dlp/rc.com"
      - "C:/Portable Applications/yt-dlp/rc.exe"
      - "C:/Portable Applications/yt-dlp/rc"
      - "C:/Program Files/Calibre2/rc.com"
      - "C:/Program Files/Calibre2/rc.exe"
      - "C:/Program Files/Calibre2/rc"
      - "C:/Program Files/Git/cmd/rc.com"
      - "C:/Program Files/Git/cmd/rc.exe"
      - "C:/Program Files/Git/cmd/rc"
      - "C:/Program Files/Docker/Docker/resources/bin/rc.com"
      - "C:/Program Files/Docker/Docker/resources/bin/rc.exe"
      - "C:/Program Files/Docker/Docker/resources/bin/rc"
      - "C:/Portable Applications/zig/rc.com"
      - "C:/Portable Applications/zig/rc.exe"
      - "C:/Portable Applications/zig/rc"
      - "C:/Program Files/nodejs/rc.com"
      - "C:/Program Files/nodejs/rc.exe"
      - "C:/Program Files/nodejs/rc"
      - "C:/Program Files/dotnet/rc.com"
      - "C:/Program Files/dotnet/rc.exe"
      - "C:/Program Files/dotnet/rc"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/rc.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/rc.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/rc"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/rc.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/rc.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/rc"
      - "C:/Users/<USER>/AppData/Local/pnpm/rc.com"
      - "C:/Users/<USER>/AppData/Local/pnpm/rc.exe"
      - "C:/Users/<USER>/AppData/Local/pnpm/rc"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/rc.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/rc.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/rc"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/rc.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/rc.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/rc"
      - "C:/Users/<USER>/.cargo/bin/rc.com"
      - "C:/Users/<USER>/.cargo/bin/rc.exe"
      - "C:/Users/<USER>/.cargo/bin/rc"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/rc.com"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/rc.exe"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/rc"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/rc.com"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/rc.exe"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/rc"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/rc.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/rc.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/rc"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/rc.com"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/rc.exe"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/rc"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/rc.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/rc.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/rc"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/rc.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/rc.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/rc"
      - "C:/Users/<USER>/.deno/bin/rc.com"
      - "C:/Users/<USER>/.deno/bin/rc.exe"
      - "C:/Users/<USER>/.deno/bin/rc"
      - "C:/Users/<USER>/AppData/Roaming/npm/rc.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/rc.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/rc"
      - "C:/Program Files/bin/rc.com"
      - "C:/Program Files/bin/rc.exe"
      - "C:/Program Files/bin/rc"
      - "C:/Program Files/sbin/rc.com"
      - "C:/Program Files/sbin/rc.exe"
      - "C:/Program Files/sbin/rc"
      - "C:/Program Files/rc.com"
      - "C:/Program Files/rc.exe"
      - "C:/Program Files/rc"
      - "C:/Program Files (x86)/bin/rc.com"
      - "C:/Program Files (x86)/bin/rc.exe"
      - "C:/Program Files (x86)/bin/rc"
      - "C:/Program Files (x86)/sbin/rc.com"
      - "C:/Program Files (x86)/sbin/rc.exe"
      - "C:/Program Files (x86)/sbin/rc"
      - "C:/Program Files (x86)/rc.com"
      - "C:/Program Files (x86)/rc.exe"
      - "C:/Program Files (x86)/rc"
      - "C:/Program Files/CMake/bin/rc.com"
      - "C:/Program Files/CMake/bin/rc.exe"
      - "C:/Program Files/CMake/bin/rc"
      - "C:/Program Files/CMake/sbin/rc.com"
      - "C:/Program Files/CMake/sbin/rc.exe"
      - "C:/Program Files/CMake/sbin/rc"
      - "C:/Program Files/CMake/rc.com"
      - "C:/Program Files/CMake/rc.exe"
      - "C:/Program Files/CMake/rc"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\Program Files\\Java\\jdk-21\\bin"
        - "C:\\Program Files\\Common Files\\Oracle\\Java\\javapath"
        - "C:\\Program Files\\Microsoft\\jdk-*********-hotspot\\bin"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\ProgramData\\chocolatey\\bin"
        - "C:\\Program Files\\Microsoft VS Code\\bin"
        - "C:\\Program Files\\Open Steno Project\\Plover 5.0.0.dev1"
        - "C:\\Portable Applications"
        - "C:\\Portable Applications\\yt-dlp"
        - "C:\\Program Files\\Calibre2\\"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "C:\\Portable Applications\\zig"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\pnpm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\"
        - "C:\\Users\\<USER>\\.cargo\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\platform-tools"
        - "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\tools"
        - "C:\\Users\\<USER>\\AppData\\Local\\pnpm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Ollama"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Users\\<USER>\\.deno\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
      CMAKE_INSTALL_PREFIX: "C:/Program Files (x86)/."
      CMAKE_SYSTEM_PREFIX_PATH:
        - "C:/Program Files"
        - "C:/Program Files (x86)"
        - "C:/Program Files/CMake"
        - "C:/Program Files (x86)/."
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:83 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting C compiler ABI info"
    directories:
      source: "C:/Users/<USER>/_Items/Dev/GitHub/scale/aider-quality-cpp/build/CMakeFiles/CMakeScratch/TryCompile-4eiud7"
      binary: "C:/Users/<USER>/_Items/Dev/GitHub/scale/aider-quality-cpp/build/CMakeFiles/CMakeScratch/TryCompile-4eiud7"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_C_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/_Items/Dev/GitHub/scale/aider-quality-cpp/build/CMakeFiles/CMakeScratch/TryCompile-4eiud7'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_c81b0.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.13.19+0d9f5a35a for .NET Framework
        Build started 7/10/2025 5:48:29 PM.
        
        Project "C:\\Users\\<USER>\\_Items\\Dev\\GitHub\\scale\\aider-quality-cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-4eiud7\\cmTC_c81b0.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_c81b0.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "C:\\Users\\<USER>\\_Items\\Dev\\GitHub\\scale\\aider-quality-cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-4eiud7\\Debug\\".
          Creating directory "cmTC_c81b0.dir\\Debug\\cmTC_c81b0.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_c81b0.dir\\Debug\\cmTC_c81b0.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_c81b0.dir\\Debug\\cmTC_c81b0.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Fo"cmTC_c81b0.dir\\Debug\\\\" /Fd"cmTC_c81b0.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-4.1\\Modules\\CMakeCCompilerABI.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.43.34810 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Fo"cmTC_c81b0.dir\\Debug\\\\" /Fd"cmTC_c81b0.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-4.1\\Modules\\CMakeCCompilerABI.c"
          CMakeCCompilerABI.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\link.exe /OUT:"C:\\Users\\<USER>\\_Items\\Dev\\GitHub\\scale\\aider-quality-cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-4eiud7\\Debug\\cmTC_c81b0.exe" /INCREMENTAL /ILK:"cmTC_c81b0.dir\\Debug\\cmTC_c81b0.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/Users/<USER>/_Items/Dev/GitHub/scale/aider-quality-cpp/build/CMakeFiles/CMakeScratch/TryCompile-4eiud7/Debug/cmTC_c81b0.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /IMPLIB:"C:/Users/<USER>/_Items/Dev/GitHub/scale/aider-quality-cpp/build/CMakeFiles/CMakeScratch/TryCompile-4eiud7/Debug/cmTC_c81b0.lib" /MACHINE:X64  /machine:x64 cmTC_c81b0.dir\\Debug\\CMakeCCompilerABI.obj
          cmTC_c81b0.vcxproj -> C:\\Users\\<USER>\\_Items\\Dev\\GitHub\\scale\\aider-quality-cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-4eiud7\\Debug\\cmTC_c81b0.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_c81b0.dir\\Debug\\cmTC_c81b0.tlog\\unsuccessfulbuild".
          Touching "cmTC_c81b0.dir\\Debug\\cmTC_c81b0.tlog\\cmTC_c81b0.lastbuildstate".
        Done Building Project "C:\\Users\\<USER>\\_Items\\Dev\\GitHub\\scale\\aider-quality-cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-4eiud7\\cmTC_c81b0.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:00.68
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:253 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed C implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|link\\.exe|lld-link(\\.exe)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|link\\.exe|lld-link(\\.exe)?))("|,| |$)]
        linker tool for 'C': C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/HostX64/x64/link.exe
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:299 (cmake_determine_linker_id)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Running the C compiler's linker: "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/HostX64/x64/link.exe" "-v"
      Microsoft (R) Incremental Linker Version 14.43.34810.0
      Copyright (C) Microsoft Corporation.  All rights reserved.
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:83 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "C:/Users/<USER>/_Items/Dev/GitHub/scale/aider-quality-cpp/build/CMakeFiles/CMakeScratch/TryCompile-30fku6"
      binary: "C:/Users/<USER>/_Items/Dev/GitHub/scale/aider-quality-cpp/build/CMakeFiles/CMakeScratch/TryCompile-30fku6"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /W3 /GR /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_CXX_SCAN_FOR_MODULES: "OFF"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/_Items/Dev/GitHub/scale/aider-quality-cpp/build/CMakeFiles/CMakeScratch/TryCompile-30fku6'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_edbd1.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.13.19+0d9f5a35a for .NET Framework
        Build started 7/10/2025 5:48:30 PM.
        
        Project "C:\\Users\\<USER>\\_Items\\Dev\\GitHub\\scale\\aider-quality-cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-30fku6\\cmTC_edbd1.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_edbd1.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "C:\\Users\\<USER>\\_Items\\Dev\\GitHub\\scale\\aider-quality-cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-30fku6\\Debug\\".
          Creating directory "cmTC_edbd1.dir\\Debug\\cmTC_edbd1.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_edbd1.dir\\Debug\\cmTC_edbd1.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_edbd1.dir\\Debug\\cmTC_edbd1.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /GR /Fo"cmTC_edbd1.dir\\Debug\\\\" /Fd"cmTC_edbd1.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TP /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-4.1\\Modules\\CMakeCXXCompilerABI.cpp"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.43.34810 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /GR /Fo"cmTC_edbd1.dir\\Debug\\\\" /Fd"cmTC_edbd1.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TP /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-4.1\\Modules\\CMakeCXXCompilerABI.cpp"
          CMakeCXXCompilerABI.cpp
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\link.exe /OUT:"C:\\Users\\<USER>\\_Items\\Dev\\GitHub\\scale\\aider-quality-cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-30fku6\\Debug\\cmTC_edbd1.exe" /INCREMENTAL /ILK:"cmTC_edbd1.dir\\Debug\\cmTC_edbd1.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/Users/<USER>/_Items/Dev/GitHub/scale/aider-quality-cpp/build/CMakeFiles/CMakeScratch/TryCompile-30fku6/Debug/cmTC_edbd1.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /IMPLIB:"C:/Users/<USER>/_Items/Dev/GitHub/scale/aider-quality-cpp/build/CMakeFiles/CMakeScratch/TryCompile-30fku6/Debug/cmTC_edbd1.lib" /MACHINE:X64  /machine:x64 cmTC_edbd1.dir\\Debug\\CMakeCXXCompilerABI.obj
          cmTC_edbd1.vcxproj -> C:\\Users\\<USER>\\_Items\\Dev\\GitHub\\scale\\aider-quality-cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-30fku6\\Debug\\cmTC_edbd1.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_edbd1.dir\\Debug\\cmTC_edbd1.tlog\\unsuccessfulbuild".
          Touching "cmTC_edbd1.dir\\Debug\\cmTC_edbd1.tlog\\cmTC_edbd1.lastbuildstate".
        Done Building Project "C:\\Users\\<USER>\\_Items\\Dev\\GitHub\\scale\\aider-quality-cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-30fku6\\cmTC_edbd1.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:00.70
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:253 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed CXX implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|link\\.exe|lld-link(\\.exe)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|link\\.exe|lld-link(\\.exe)?))("|,| |$)]
        linker tool for 'CXX': C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/HostX64/x64/link.exe
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:299 (cmake_determine_linker_id)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Running the CXX compiler's linker: "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/HostX64/x64/link.exe" "-v"
      Microsoft (R) Incremental Linker Version 14.43.34810.0
      Copyright (C) Microsoft Corporation.  All rights reserved.
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CTest.cmake:188 (find_program)"
      - "CMakeLists.txt:6 (include)"
    mode: "program"
    variable: "MEMORYCHECK_COMMAND"
    description: "Path to the memory checking command, used for memory error detection."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "purify"
      - "valgrind"
      - "boundscheck"
      - "drmemory"
      - "cuda-memcheck"
      - "compute-sanitizer"
    candidate_directories:
      - "C:/Program Files/Java/jdk-21/bin/"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/ProgramData/chocolatey/bin/"
      - "C:/Program Files/Microsoft VS Code/bin/"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/"
      - "C:/Portable Applications/"
      - "C:/Portable Applications/yt-dlp/"
      - "C:/Program Files/Calibre2/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/Docker/Docker/resources/bin/"
      - "C:/Portable Applications/zig/"
      - "C:/Program Files/nodejs/"
      - "C:/Program Files/dotnet/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/"
      - "C:/Users/<USER>/AppData/Local/pnpm/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/"
      - "C:/Users/<USER>/.cargo/bin/"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Users/<USER>/.deno/bin/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "C:/Program Files/bin/"
      - "C:/Program Files/sbin/"
      - "C:/Program Files/"
      - "C:/Program Files (x86)/bin/"
      - "C:/Program Files (x86)/sbin/"
      - "C:/Program Files (x86)/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Program Files/CMake/sbin/"
      - "C:/Program Files/CMake/"
      - "/REGISTRY-NOTFOUND/"
    searched_directories:
      - "C:/Program Files/Java/jdk-21/bin/purify.com"
      - "C:/Program Files/Java/jdk-21/bin/purify.exe"
      - "C:/Program Files/Java/jdk-21/bin/purify"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/purify.com"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/purify.exe"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/purify"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/purify.com"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/purify.exe"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/purify"
      - "C:/Windows/System32/purify.com"
      - "C:/Windows/System32/purify.exe"
      - "C:/Windows/System32/purify"
      - "C:/Windows/purify.com"
      - "C:/Windows/purify.exe"
      - "C:/Windows/purify"
      - "C:/Windows/System32/wbem/purify.com"
      - "C:/Windows/System32/wbem/purify.exe"
      - "C:/Windows/System32/wbem/purify"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/purify.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/purify.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/purify"
      - "C:/Windows/System32/OpenSSH/purify.com"
      - "C:/Windows/System32/OpenSSH/purify.exe"
      - "C:/Windows/System32/OpenSSH/purify"
      - "C:/ProgramData/chocolatey/bin/purify.com"
      - "C:/ProgramData/chocolatey/bin/purify.exe"
      - "C:/ProgramData/chocolatey/bin/purify"
      - "C:/Program Files/Microsoft VS Code/bin/purify.com"
      - "C:/Program Files/Microsoft VS Code/bin/purify.exe"
      - "C:/Program Files/Microsoft VS Code/bin/purify"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/purify.com"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/purify.exe"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/purify"
      - "C:/Portable Applications/purify.com"
      - "C:/Portable Applications/purify.exe"
      - "C:/Portable Applications/purify"
      - "C:/Portable Applications/yt-dlp/purify.com"
      - "C:/Portable Applications/yt-dlp/purify.exe"
      - "C:/Portable Applications/yt-dlp/purify"
      - "C:/Program Files/Calibre2/purify.com"
      - "C:/Program Files/Calibre2/purify.exe"
      - "C:/Program Files/Calibre2/purify"
      - "C:/Program Files/Git/cmd/purify.com"
      - "C:/Program Files/Git/cmd/purify.exe"
      - "C:/Program Files/Git/cmd/purify"
      - "C:/Program Files/Docker/Docker/resources/bin/purify.com"
      - "C:/Program Files/Docker/Docker/resources/bin/purify.exe"
      - "C:/Program Files/Docker/Docker/resources/bin/purify"
      - "C:/Portable Applications/zig/purify.com"
      - "C:/Portable Applications/zig/purify.exe"
      - "C:/Portable Applications/zig/purify"
      - "C:/Program Files/nodejs/purify.com"
      - "C:/Program Files/nodejs/purify.exe"
      - "C:/Program Files/nodejs/purify"
      - "C:/Program Files/dotnet/purify.com"
      - "C:/Program Files/dotnet/purify.exe"
      - "C:/Program Files/dotnet/purify"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/purify.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/purify.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/purify"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/purify.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/purify.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/purify"
      - "C:/Users/<USER>/AppData/Local/pnpm/purify.com"
      - "C:/Users/<USER>/AppData/Local/pnpm/purify.exe"
      - "C:/Users/<USER>/AppData/Local/pnpm/purify"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/purify.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/purify.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/purify"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/purify.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/purify.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/purify"
      - "C:/Users/<USER>/.cargo/bin/purify.com"
      - "C:/Users/<USER>/.cargo/bin/purify.exe"
      - "C:/Users/<USER>/.cargo/bin/purify"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/purify.com"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/purify.exe"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/purify"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/purify.com"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/purify.exe"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/purify"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/purify.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/purify.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/purify"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/purify.com"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/purify.exe"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/purify"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/purify.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/purify.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/purify"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/purify.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/purify.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/purify"
      - "C:/Users/<USER>/.deno/bin/purify.com"
      - "C:/Users/<USER>/.deno/bin/purify.exe"
      - "C:/Users/<USER>/.deno/bin/purify"
      - "C:/Users/<USER>/AppData/Roaming/npm/purify.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/purify.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/purify"
      - "C:/Program Files/bin/purify.com"
      - "C:/Program Files/bin/purify.exe"
      - "C:/Program Files/bin/purify"
      - "C:/Program Files/sbin/purify.com"
      - "C:/Program Files/sbin/purify.exe"
      - "C:/Program Files/sbin/purify"
      - "C:/Program Files/purify.com"
      - "C:/Program Files/purify.exe"
      - "C:/Program Files/purify"
      - "C:/Program Files (x86)/bin/purify.com"
      - "C:/Program Files (x86)/bin/purify.exe"
      - "C:/Program Files (x86)/bin/purify"
      - "C:/Program Files (x86)/sbin/purify.com"
      - "C:/Program Files (x86)/sbin/purify.exe"
      - "C:/Program Files (x86)/sbin/purify"
      - "C:/Program Files (x86)/purify.com"
      - "C:/Program Files (x86)/purify.exe"
      - "C:/Program Files (x86)/purify"
      - "C:/Program Files/CMake/bin/purify.com"
      - "C:/Program Files/CMake/bin/purify.exe"
      - "C:/Program Files/CMake/bin/purify"
      - "C:/Program Files/CMake/sbin/purify.com"
      - "C:/Program Files/CMake/sbin/purify.exe"
      - "C:/Program Files/CMake/sbin/purify"
      - "C:/Program Files/CMake/purify.com"
      - "C:/Program Files/CMake/purify.exe"
      - "C:/Program Files/CMake/purify"
      - "/REGISTRY-NOTFOUND/purify.com"
      - "/REGISTRY-NOTFOUND/purify.exe"
      - "/REGISTRY-NOTFOUND/purify"
      - "C:/Program Files/Java/jdk-21/bin/valgrind.com"
      - "C:/Program Files/Java/jdk-21/bin/valgrind.exe"
      - "C:/Program Files/Java/jdk-21/bin/valgrind"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/valgrind.com"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/valgrind.exe"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/valgrind"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/valgrind.com"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/valgrind.exe"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/valgrind"
      - "C:/Windows/System32/valgrind.com"
      - "C:/Windows/System32/valgrind.exe"
      - "C:/Windows/System32/valgrind"
      - "C:/Windows/valgrind.com"
      - "C:/Windows/valgrind.exe"
      - "C:/Windows/valgrind"
      - "C:/Windows/System32/wbem/valgrind.com"
      - "C:/Windows/System32/wbem/valgrind.exe"
      - "C:/Windows/System32/wbem/valgrind"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/valgrind.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/valgrind.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/valgrind"
      - "C:/Windows/System32/OpenSSH/valgrind.com"
      - "C:/Windows/System32/OpenSSH/valgrind.exe"
      - "C:/Windows/System32/OpenSSH/valgrind"
      - "C:/ProgramData/chocolatey/bin/valgrind.com"
      - "C:/ProgramData/chocolatey/bin/valgrind.exe"
      - "C:/ProgramData/chocolatey/bin/valgrind"
      - "C:/Program Files/Microsoft VS Code/bin/valgrind.com"
      - "C:/Program Files/Microsoft VS Code/bin/valgrind.exe"
      - "C:/Program Files/Microsoft VS Code/bin/valgrind"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/valgrind.com"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/valgrind.exe"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/valgrind"
      - "C:/Portable Applications/valgrind.com"
      - "C:/Portable Applications/valgrind.exe"
      - "C:/Portable Applications/valgrind"
      - "C:/Portable Applications/yt-dlp/valgrind.com"
      - "C:/Portable Applications/yt-dlp/valgrind.exe"
      - "C:/Portable Applications/yt-dlp/valgrind"
      - "C:/Program Files/Calibre2/valgrind.com"
      - "C:/Program Files/Calibre2/valgrind.exe"
      - "C:/Program Files/Calibre2/valgrind"
      - "C:/Program Files/Git/cmd/valgrind.com"
      - "C:/Program Files/Git/cmd/valgrind.exe"
      - "C:/Program Files/Git/cmd/valgrind"
      - "C:/Program Files/Docker/Docker/resources/bin/valgrind.com"
      - "C:/Program Files/Docker/Docker/resources/bin/valgrind.exe"
      - "C:/Program Files/Docker/Docker/resources/bin/valgrind"
      - "C:/Portable Applications/zig/valgrind.com"
      - "C:/Portable Applications/zig/valgrind.exe"
      - "C:/Portable Applications/zig/valgrind"
      - "C:/Program Files/nodejs/valgrind.com"
      - "C:/Program Files/nodejs/valgrind.exe"
      - "C:/Program Files/nodejs/valgrind"
      - "C:/Program Files/dotnet/valgrind.com"
      - "C:/Program Files/dotnet/valgrind.exe"
      - "C:/Program Files/dotnet/valgrind"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/valgrind.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/valgrind.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/valgrind"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/valgrind.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/valgrind.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/valgrind"
      - "C:/Users/<USER>/AppData/Local/pnpm/valgrind.com"
      - "C:/Users/<USER>/AppData/Local/pnpm/valgrind.exe"
      - "C:/Users/<USER>/AppData/Local/pnpm/valgrind"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/valgrind.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/valgrind.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/valgrind"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/valgrind.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/valgrind.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/valgrind"
      - "C:/Users/<USER>/.cargo/bin/valgrind.com"
      - "C:/Users/<USER>/.cargo/bin/valgrind.exe"
      - "C:/Users/<USER>/.cargo/bin/valgrind"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/valgrind.com"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/valgrind.exe"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/valgrind"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/valgrind.com"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/valgrind.exe"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/valgrind"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/valgrind.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/valgrind.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/valgrind"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/valgrind.com"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/valgrind.exe"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/valgrind"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/valgrind.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/valgrind.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/valgrind"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/valgrind.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/valgrind.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/valgrind"
      - "C:/Users/<USER>/.deno/bin/valgrind.com"
      - "C:/Users/<USER>/.deno/bin/valgrind.exe"
      - "C:/Users/<USER>/.deno/bin/valgrind"
      - "C:/Users/<USER>/AppData/Roaming/npm/valgrind.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/valgrind.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/valgrind"
      - "C:/Program Files/bin/valgrind.com"
      - "C:/Program Files/bin/valgrind.exe"
      - "C:/Program Files/bin/valgrind"
      - "C:/Program Files/sbin/valgrind.com"
      - "C:/Program Files/sbin/valgrind.exe"
      - "C:/Program Files/sbin/valgrind"
      - "C:/Program Files/valgrind.com"
      - "C:/Program Files/valgrind.exe"
      - "C:/Program Files/valgrind"
      - "C:/Program Files (x86)/bin/valgrind.com"
      - "C:/Program Files (x86)/bin/valgrind.exe"
      - "C:/Program Files (x86)/bin/valgrind"
      - "C:/Program Files (x86)/sbin/valgrind.com"
      - "C:/Program Files (x86)/sbin/valgrind.exe"
      - "C:/Program Files (x86)/sbin/valgrind"
      - "C:/Program Files (x86)/valgrind.com"
      - "C:/Program Files (x86)/valgrind.exe"
      - "C:/Program Files (x86)/valgrind"
      - "C:/Program Files/CMake/bin/valgrind.com"
      - "C:/Program Files/CMake/bin/valgrind.exe"
      - "C:/Program Files/CMake/bin/valgrind"
      - "C:/Program Files/CMake/sbin/valgrind.com"
      - "C:/Program Files/CMake/sbin/valgrind.exe"
      - "C:/Program Files/CMake/sbin/valgrind"
      - "C:/Program Files/CMake/valgrind.com"
      - "C:/Program Files/CMake/valgrind.exe"
      - "C:/Program Files/CMake/valgrind"
      - "/REGISTRY-NOTFOUND/valgrind.com"
      - "/REGISTRY-NOTFOUND/valgrind.exe"
      - "/REGISTRY-NOTFOUND/valgrind"
      - "C:/Program Files/Java/jdk-21/bin/boundscheck.com"
      - "C:/Program Files/Java/jdk-21/bin/boundscheck.exe"
      - "C:/Program Files/Java/jdk-21/bin/boundscheck"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/boundscheck.com"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/boundscheck.exe"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/boundscheck"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/boundscheck.com"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/boundscheck.exe"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/boundscheck"
      - "C:/Windows/System32/boundscheck.com"
      - "C:/Windows/System32/boundscheck.exe"
      - "C:/Windows/System32/boundscheck"
      - "C:/Windows/boundscheck.com"
      - "C:/Windows/boundscheck.exe"
      - "C:/Windows/boundscheck"
      - "C:/Windows/System32/wbem/boundscheck.com"
      - "C:/Windows/System32/wbem/boundscheck.exe"
      - "C:/Windows/System32/wbem/boundscheck"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/boundscheck.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/boundscheck.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/boundscheck"
      - "C:/Windows/System32/OpenSSH/boundscheck.com"
      - "C:/Windows/System32/OpenSSH/boundscheck.exe"
      - "C:/Windows/System32/OpenSSH/boundscheck"
      - "C:/ProgramData/chocolatey/bin/boundscheck.com"
      - "C:/ProgramData/chocolatey/bin/boundscheck.exe"
      - "C:/ProgramData/chocolatey/bin/boundscheck"
      - "C:/Program Files/Microsoft VS Code/bin/boundscheck.com"
      - "C:/Program Files/Microsoft VS Code/bin/boundscheck.exe"
      - "C:/Program Files/Microsoft VS Code/bin/boundscheck"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/boundscheck.com"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/boundscheck.exe"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/boundscheck"
      - "C:/Portable Applications/boundscheck.com"
      - "C:/Portable Applications/boundscheck.exe"
      - "C:/Portable Applications/boundscheck"
      - "C:/Portable Applications/yt-dlp/boundscheck.com"
      - "C:/Portable Applications/yt-dlp/boundscheck.exe"
      - "C:/Portable Applications/yt-dlp/boundscheck"
      - "C:/Program Files/Calibre2/boundscheck.com"
      - "C:/Program Files/Calibre2/boundscheck.exe"
      - "C:/Program Files/Calibre2/boundscheck"
      - "C:/Program Files/Git/cmd/boundscheck.com"
      - "C:/Program Files/Git/cmd/boundscheck.exe"
      - "C:/Program Files/Git/cmd/boundscheck"
      - "C:/Program Files/Docker/Docker/resources/bin/boundscheck.com"
      - "C:/Program Files/Docker/Docker/resources/bin/boundscheck.exe"
      - "C:/Program Files/Docker/Docker/resources/bin/boundscheck"
      - "C:/Portable Applications/zig/boundscheck.com"
      - "C:/Portable Applications/zig/boundscheck.exe"
      - "C:/Portable Applications/zig/boundscheck"
      - "C:/Program Files/nodejs/boundscheck.com"
      - "C:/Program Files/nodejs/boundscheck.exe"
      - "C:/Program Files/nodejs/boundscheck"
      - "C:/Program Files/dotnet/boundscheck.com"
      - "C:/Program Files/dotnet/boundscheck.exe"
      - "C:/Program Files/dotnet/boundscheck"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/boundscheck.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/boundscheck.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/boundscheck"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/boundscheck.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/boundscheck.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/boundscheck"
      - "C:/Users/<USER>/AppData/Local/pnpm/boundscheck.com"
      - "C:/Users/<USER>/AppData/Local/pnpm/boundscheck.exe"
      - "C:/Users/<USER>/AppData/Local/pnpm/boundscheck"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/boundscheck.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/boundscheck.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/boundscheck"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/boundscheck.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/boundscheck.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/boundscheck"
      - "C:/Users/<USER>/.cargo/bin/boundscheck.com"
      - "C:/Users/<USER>/.cargo/bin/boundscheck.exe"
      - "C:/Users/<USER>/.cargo/bin/boundscheck"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/boundscheck.com"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/boundscheck.exe"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/boundscheck"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/boundscheck.com"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/boundscheck.exe"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/boundscheck"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/boundscheck.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/boundscheck.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/boundscheck"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/boundscheck.com"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/boundscheck.exe"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/boundscheck"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/boundscheck.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/boundscheck.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/boundscheck"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/boundscheck.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/boundscheck.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/boundscheck"
      - "C:/Users/<USER>/.deno/bin/boundscheck.com"
      - "C:/Users/<USER>/.deno/bin/boundscheck.exe"
      - "C:/Users/<USER>/.deno/bin/boundscheck"
      - "C:/Users/<USER>/AppData/Roaming/npm/boundscheck.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/boundscheck.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/boundscheck"
      - "C:/Program Files/bin/boundscheck.com"
      - "C:/Program Files/bin/boundscheck.exe"
      - "C:/Program Files/bin/boundscheck"
      - "C:/Program Files/sbin/boundscheck.com"
      - "C:/Program Files/sbin/boundscheck.exe"
      - "C:/Program Files/sbin/boundscheck"
      - "C:/Program Files/boundscheck.com"
      - "C:/Program Files/boundscheck.exe"
      - "C:/Program Files/boundscheck"
      - "C:/Program Files (x86)/bin/boundscheck.com"
      - "C:/Program Files (x86)/bin/boundscheck.exe"
      - "C:/Program Files (x86)/bin/boundscheck"
      - "C:/Program Files (x86)/sbin/boundscheck.com"
      - "C:/Program Files (x86)/sbin/boundscheck.exe"
      - "C:/Program Files (x86)/sbin/boundscheck"
      - "C:/Program Files (x86)/boundscheck.com"
      - "C:/Program Files (x86)/boundscheck.exe"
      - "C:/Program Files (x86)/boundscheck"
      - "C:/Program Files/CMake/bin/boundscheck.com"
      - "C:/Program Files/CMake/bin/boundscheck.exe"
      - "C:/Program Files/CMake/bin/boundscheck"
      - "C:/Program Files/CMake/sbin/boundscheck.com"
      - "C:/Program Files/CMake/sbin/boundscheck.exe"
      - "C:/Program Files/CMake/sbin/boundscheck"
      - "C:/Program Files/CMake/boundscheck.com"
      - "C:/Program Files/CMake/boundscheck.exe"
      - "C:/Program Files/CMake/boundscheck"
      - "/REGISTRY-NOTFOUND/boundscheck.com"
      - "/REGISTRY-NOTFOUND/boundscheck.exe"
      - "/REGISTRY-NOTFOUND/boundscheck"
      - "C:/Program Files/Java/jdk-21/bin/drmemory.com"
      - "C:/Program Files/Java/jdk-21/bin/drmemory.exe"
      - "C:/Program Files/Java/jdk-21/bin/drmemory"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/drmemory.com"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/drmemory.exe"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/drmemory"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/drmemory.com"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/drmemory.exe"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/drmemory"
      - "C:/Windows/System32/drmemory.com"
      - "C:/Windows/System32/drmemory.exe"
      - "C:/Windows/System32/drmemory"
      - "C:/Windows/drmemory.com"
      - "C:/Windows/drmemory.exe"
      - "C:/Windows/drmemory"
      - "C:/Windows/System32/wbem/drmemory.com"
      - "C:/Windows/System32/wbem/drmemory.exe"
      - "C:/Windows/System32/wbem/drmemory"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/drmemory.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/drmemory.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/drmemory"
      - "C:/Windows/System32/OpenSSH/drmemory.com"
      - "C:/Windows/System32/OpenSSH/drmemory.exe"
      - "C:/Windows/System32/OpenSSH/drmemory"
      - "C:/ProgramData/chocolatey/bin/drmemory.com"
      - "C:/ProgramData/chocolatey/bin/drmemory.exe"
      - "C:/ProgramData/chocolatey/bin/drmemory"
      - "C:/Program Files/Microsoft VS Code/bin/drmemory.com"
      - "C:/Program Files/Microsoft VS Code/bin/drmemory.exe"
      - "C:/Program Files/Microsoft VS Code/bin/drmemory"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/drmemory.com"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/drmemory.exe"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/drmemory"
      - "C:/Portable Applications/drmemory.com"
      - "C:/Portable Applications/drmemory.exe"
      - "C:/Portable Applications/drmemory"
      - "C:/Portable Applications/yt-dlp/drmemory.com"
      - "C:/Portable Applications/yt-dlp/drmemory.exe"
      - "C:/Portable Applications/yt-dlp/drmemory"
      - "C:/Program Files/Calibre2/drmemory.com"
      - "C:/Program Files/Calibre2/drmemory.exe"
      - "C:/Program Files/Calibre2/drmemory"
      - "C:/Program Files/Git/cmd/drmemory.com"
      - "C:/Program Files/Git/cmd/drmemory.exe"
      - "C:/Program Files/Git/cmd/drmemory"
      - "C:/Program Files/Docker/Docker/resources/bin/drmemory.com"
      - "C:/Program Files/Docker/Docker/resources/bin/drmemory.exe"
      - "C:/Program Files/Docker/Docker/resources/bin/drmemory"
      - "C:/Portable Applications/zig/drmemory.com"
      - "C:/Portable Applications/zig/drmemory.exe"
      - "C:/Portable Applications/zig/drmemory"
      - "C:/Program Files/nodejs/drmemory.com"
      - "C:/Program Files/nodejs/drmemory.exe"
      - "C:/Program Files/nodejs/drmemory"
      - "C:/Program Files/dotnet/drmemory.com"
      - "C:/Program Files/dotnet/drmemory.exe"
      - "C:/Program Files/dotnet/drmemory"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/drmemory.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/drmemory.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/drmemory"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/drmemory.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/drmemory.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/drmemory"
      - "C:/Users/<USER>/AppData/Local/pnpm/drmemory.com"
      - "C:/Users/<USER>/AppData/Local/pnpm/drmemory.exe"
      - "C:/Users/<USER>/AppData/Local/pnpm/drmemory"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/drmemory.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/drmemory.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/drmemory"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/drmemory.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/drmemory.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/drmemory"
      - "C:/Users/<USER>/.cargo/bin/drmemory.com"
      - "C:/Users/<USER>/.cargo/bin/drmemory.exe"
      - "C:/Users/<USER>/.cargo/bin/drmemory"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/drmemory.com"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/drmemory.exe"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/drmemory"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/drmemory.com"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/drmemory.exe"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/drmemory"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/drmemory.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/drmemory.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/drmemory"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/drmemory.com"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/drmemory.exe"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/drmemory"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/drmemory.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/drmemory.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/drmemory"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/drmemory.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/drmemory.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/drmemory"
      - "C:/Users/<USER>/.deno/bin/drmemory.com"
      - "C:/Users/<USER>/.deno/bin/drmemory.exe"
      - "C:/Users/<USER>/.deno/bin/drmemory"
      - "C:/Users/<USER>/AppData/Roaming/npm/drmemory.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/drmemory.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/drmemory"
      - "C:/Program Files/bin/drmemory.com"
      - "C:/Program Files/bin/drmemory.exe"
      - "C:/Program Files/bin/drmemory"
      - "C:/Program Files/sbin/drmemory.com"
      - "C:/Program Files/sbin/drmemory.exe"
      - "C:/Program Files/sbin/drmemory"
      - "C:/Program Files/drmemory.com"
      - "C:/Program Files/drmemory.exe"
      - "C:/Program Files/drmemory"
      - "C:/Program Files (x86)/bin/drmemory.com"
      - "C:/Program Files (x86)/bin/drmemory.exe"
      - "C:/Program Files (x86)/bin/drmemory"
      - "C:/Program Files (x86)/sbin/drmemory.com"
      - "C:/Program Files (x86)/sbin/drmemory.exe"
      - "C:/Program Files (x86)/sbin/drmemory"
      - "C:/Program Files (x86)/drmemory.com"
      - "C:/Program Files (x86)/drmemory.exe"
      - "C:/Program Files (x86)/drmemory"
      - "C:/Program Files/CMake/bin/drmemory.com"
      - "C:/Program Files/CMake/bin/drmemory.exe"
      - "C:/Program Files/CMake/bin/drmemory"
      - "C:/Program Files/CMake/sbin/drmemory.com"
      - "C:/Program Files/CMake/sbin/drmemory.exe"
      - "C:/Program Files/CMake/sbin/drmemory"
      - "C:/Program Files/CMake/drmemory.com"
      - "C:/Program Files/CMake/drmemory.exe"
      - "C:/Program Files/CMake/drmemory"
      - "/REGISTRY-NOTFOUND/drmemory.com"
      - "/REGISTRY-NOTFOUND/drmemory.exe"
      - "/REGISTRY-NOTFOUND/drmemory"
      - "C:/Program Files/Java/jdk-21/bin/cuda-memcheck.com"
      - "C:/Program Files/Java/jdk-21/bin/cuda-memcheck.exe"
      - "C:/Program Files/Java/jdk-21/bin/cuda-memcheck"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/cuda-memcheck.com"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/cuda-memcheck.exe"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/cuda-memcheck"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/cuda-memcheck.com"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/cuda-memcheck.exe"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/cuda-memcheck"
      - "C:/Windows/System32/cuda-memcheck.com"
      - "C:/Windows/System32/cuda-memcheck.exe"
      - "C:/Windows/System32/cuda-memcheck"
      - "C:/Windows/cuda-memcheck.com"
      - "C:/Windows/cuda-memcheck.exe"
      - "C:/Windows/cuda-memcheck"
      - "C:/Windows/System32/wbem/cuda-memcheck.com"
      - "C:/Windows/System32/wbem/cuda-memcheck.exe"
      - "C:/Windows/System32/wbem/cuda-memcheck"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/cuda-memcheck.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/cuda-memcheck.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/cuda-memcheck"
      - "C:/Windows/System32/OpenSSH/cuda-memcheck.com"
      - "C:/Windows/System32/OpenSSH/cuda-memcheck.exe"
      - "C:/Windows/System32/OpenSSH/cuda-memcheck"
      - "C:/ProgramData/chocolatey/bin/cuda-memcheck.com"
      - "C:/ProgramData/chocolatey/bin/cuda-memcheck.exe"
      - "C:/ProgramData/chocolatey/bin/cuda-memcheck"
      - "C:/Program Files/Microsoft VS Code/bin/cuda-memcheck.com"
      - "C:/Program Files/Microsoft VS Code/bin/cuda-memcheck.exe"
      - "C:/Program Files/Microsoft VS Code/bin/cuda-memcheck"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/cuda-memcheck.com"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/cuda-memcheck.exe"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/cuda-memcheck"
      - "C:/Portable Applications/cuda-memcheck.com"
      - "C:/Portable Applications/cuda-memcheck.exe"
      - "C:/Portable Applications/cuda-memcheck"
      - "C:/Portable Applications/yt-dlp/cuda-memcheck.com"
      - "C:/Portable Applications/yt-dlp/cuda-memcheck.exe"
      - "C:/Portable Applications/yt-dlp/cuda-memcheck"
      - "C:/Program Files/Calibre2/cuda-memcheck.com"
      - "C:/Program Files/Calibre2/cuda-memcheck.exe"
      - "C:/Program Files/Calibre2/cuda-memcheck"
      - "C:/Program Files/Git/cmd/cuda-memcheck.com"
      - "C:/Program Files/Git/cmd/cuda-memcheck.exe"
      - "C:/Program Files/Git/cmd/cuda-memcheck"
      - "C:/Program Files/Docker/Docker/resources/bin/cuda-memcheck.com"
      - "C:/Program Files/Docker/Docker/resources/bin/cuda-memcheck.exe"
      - "C:/Program Files/Docker/Docker/resources/bin/cuda-memcheck"
      - "C:/Portable Applications/zig/cuda-memcheck.com"
      - "C:/Portable Applications/zig/cuda-memcheck.exe"
      - "C:/Portable Applications/zig/cuda-memcheck"
      - "C:/Program Files/nodejs/cuda-memcheck.com"
      - "C:/Program Files/nodejs/cuda-memcheck.exe"
      - "C:/Program Files/nodejs/cuda-memcheck"
      - "C:/Program Files/dotnet/cuda-memcheck.com"
      - "C:/Program Files/dotnet/cuda-memcheck.exe"
      - "C:/Program Files/dotnet/cuda-memcheck"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/cuda-memcheck.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/cuda-memcheck.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/cuda-memcheck"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/cuda-memcheck.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/cuda-memcheck.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/cuda-memcheck"
      - "C:/Users/<USER>/AppData/Local/pnpm/cuda-memcheck.com"
      - "C:/Users/<USER>/AppData/Local/pnpm/cuda-memcheck.exe"
      - "C:/Users/<USER>/AppData/Local/pnpm/cuda-memcheck"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/cuda-memcheck.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/cuda-memcheck.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/cuda-memcheck"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/cuda-memcheck.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/cuda-memcheck.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/cuda-memcheck"
      - "C:/Users/<USER>/.cargo/bin/cuda-memcheck.com"
      - "C:/Users/<USER>/.cargo/bin/cuda-memcheck.exe"
      - "C:/Users/<USER>/.cargo/bin/cuda-memcheck"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/cuda-memcheck.com"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/cuda-memcheck.exe"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/cuda-memcheck"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/cuda-memcheck.com"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/cuda-memcheck.exe"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/cuda-memcheck"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/cuda-memcheck.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/cuda-memcheck.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/cuda-memcheck"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/cuda-memcheck.com"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/cuda-memcheck.exe"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/cuda-memcheck"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/cuda-memcheck.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/cuda-memcheck.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/cuda-memcheck"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/cuda-memcheck.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/cuda-memcheck.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/cuda-memcheck"
      - "C:/Users/<USER>/.deno/bin/cuda-memcheck.com"
      - "C:/Users/<USER>/.deno/bin/cuda-memcheck.exe"
      - "C:/Users/<USER>/.deno/bin/cuda-memcheck"
      - "C:/Users/<USER>/AppData/Roaming/npm/cuda-memcheck.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/cuda-memcheck.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/cuda-memcheck"
      - "C:/Program Files/bin/cuda-memcheck.com"
      - "C:/Program Files/bin/cuda-memcheck.exe"
      - "C:/Program Files/bin/cuda-memcheck"
      - "C:/Program Files/sbin/cuda-memcheck.com"
      - "C:/Program Files/sbin/cuda-memcheck.exe"
      - "C:/Program Files/sbin/cuda-memcheck"
      - "C:/Program Files/cuda-memcheck.com"
      - "C:/Program Files/cuda-memcheck.exe"
      - "C:/Program Files/cuda-memcheck"
      - "C:/Program Files (x86)/bin/cuda-memcheck.com"
      - "C:/Program Files (x86)/bin/cuda-memcheck.exe"
      - "C:/Program Files (x86)/bin/cuda-memcheck"
      - "C:/Program Files (x86)/sbin/cuda-memcheck.com"
      - "C:/Program Files (x86)/sbin/cuda-memcheck.exe"
      - "C:/Program Files (x86)/sbin/cuda-memcheck"
      - "C:/Program Files (x86)/cuda-memcheck.com"
      - "C:/Program Files (x86)/cuda-memcheck.exe"
      - "C:/Program Files (x86)/cuda-memcheck"
      - "C:/Program Files/CMake/bin/cuda-memcheck.com"
      - "C:/Program Files/CMake/bin/cuda-memcheck.exe"
      - "C:/Program Files/CMake/bin/cuda-memcheck"
      - "C:/Program Files/CMake/sbin/cuda-memcheck.com"
      - "C:/Program Files/CMake/sbin/cuda-memcheck.exe"
      - "C:/Program Files/CMake/sbin/cuda-memcheck"
      - "C:/Program Files/CMake/cuda-memcheck.com"
      - "C:/Program Files/CMake/cuda-memcheck.exe"
      - "C:/Program Files/CMake/cuda-memcheck"
      - "/REGISTRY-NOTFOUND/cuda-memcheck.com"
      - "/REGISTRY-NOTFOUND/cuda-memcheck.exe"
      - "/REGISTRY-NOTFOUND/cuda-memcheck"
      - "C:/Program Files/Java/jdk-21/bin/compute-sanitizer.com"
      - "C:/Program Files/Java/jdk-21/bin/compute-sanitizer.exe"
      - "C:/Program Files/Java/jdk-21/bin/compute-sanitizer"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/compute-sanitizer.com"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/compute-sanitizer.exe"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/compute-sanitizer"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/compute-sanitizer.com"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/compute-sanitizer.exe"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/compute-sanitizer"
      - "C:/Windows/System32/compute-sanitizer.com"
      - "C:/Windows/System32/compute-sanitizer.exe"
      - "C:/Windows/System32/compute-sanitizer"
      - "C:/Windows/compute-sanitizer.com"
      - "C:/Windows/compute-sanitizer.exe"
      - "C:/Windows/compute-sanitizer"
      - "C:/Windows/System32/wbem/compute-sanitizer.com"
      - "C:/Windows/System32/wbem/compute-sanitizer.exe"
      - "C:/Windows/System32/wbem/compute-sanitizer"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/compute-sanitizer.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/compute-sanitizer.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/compute-sanitizer"
      - "C:/Windows/System32/OpenSSH/compute-sanitizer.com"
      - "C:/Windows/System32/OpenSSH/compute-sanitizer.exe"
      - "C:/Windows/System32/OpenSSH/compute-sanitizer"
      - "C:/ProgramData/chocolatey/bin/compute-sanitizer.com"
      - "C:/ProgramData/chocolatey/bin/compute-sanitizer.exe"
      - "C:/ProgramData/chocolatey/bin/compute-sanitizer"
      - "C:/Program Files/Microsoft VS Code/bin/compute-sanitizer.com"
      - "C:/Program Files/Microsoft VS Code/bin/compute-sanitizer.exe"
      - "C:/Program Files/Microsoft VS Code/bin/compute-sanitizer"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/compute-sanitizer.com"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/compute-sanitizer.exe"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/compute-sanitizer"
      - "C:/Portable Applications/compute-sanitizer.com"
      - "C:/Portable Applications/compute-sanitizer.exe"
      - "C:/Portable Applications/compute-sanitizer"
      - "C:/Portable Applications/yt-dlp/compute-sanitizer.com"
      - "C:/Portable Applications/yt-dlp/compute-sanitizer.exe"
      - "C:/Portable Applications/yt-dlp/compute-sanitizer"
      - "C:/Program Files/Calibre2/compute-sanitizer.com"
      - "C:/Program Files/Calibre2/compute-sanitizer.exe"
      - "C:/Program Files/Calibre2/compute-sanitizer"
      - "C:/Program Files/Git/cmd/compute-sanitizer.com"
      - "C:/Program Files/Git/cmd/compute-sanitizer.exe"
      - "C:/Program Files/Git/cmd/compute-sanitizer"
      - "C:/Program Files/Docker/Docker/resources/bin/compute-sanitizer.com"
      - "C:/Program Files/Docker/Docker/resources/bin/compute-sanitizer.exe"
      - "C:/Program Files/Docker/Docker/resources/bin/compute-sanitizer"
      - "C:/Portable Applications/zig/compute-sanitizer.com"
      - "C:/Portable Applications/zig/compute-sanitizer.exe"
      - "C:/Portable Applications/zig/compute-sanitizer"
      - "C:/Program Files/nodejs/compute-sanitizer.com"
      - "C:/Program Files/nodejs/compute-sanitizer.exe"
      - "C:/Program Files/nodejs/compute-sanitizer"
      - "C:/Program Files/dotnet/compute-sanitizer.com"
      - "C:/Program Files/dotnet/compute-sanitizer.exe"
      - "C:/Program Files/dotnet/compute-sanitizer"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/compute-sanitizer.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/compute-sanitizer.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/compute-sanitizer"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/compute-sanitizer.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/compute-sanitizer.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/compute-sanitizer"
      - "C:/Users/<USER>/AppData/Local/pnpm/compute-sanitizer.com"
      - "C:/Users/<USER>/AppData/Local/pnpm/compute-sanitizer.exe"
      - "C:/Users/<USER>/AppData/Local/pnpm/compute-sanitizer"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/compute-sanitizer.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/compute-sanitizer.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/compute-sanitizer"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/compute-sanitizer.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/compute-sanitizer.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/compute-sanitizer"
      - "C:/Users/<USER>/.cargo/bin/compute-sanitizer.com"
      - "C:/Users/<USER>/.cargo/bin/compute-sanitizer.exe"
      - "C:/Users/<USER>/.cargo/bin/compute-sanitizer"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/compute-sanitizer.com"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/compute-sanitizer.exe"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/compute-sanitizer"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/compute-sanitizer.com"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/compute-sanitizer.exe"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/compute-sanitizer"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/compute-sanitizer.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/compute-sanitizer.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/compute-sanitizer"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/compute-sanitizer.com"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/compute-sanitizer.exe"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/compute-sanitizer"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/compute-sanitizer.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/compute-sanitizer.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/compute-sanitizer"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/compute-sanitizer.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/compute-sanitizer.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/compute-sanitizer"
      - "C:/Users/<USER>/.deno/bin/compute-sanitizer.com"
      - "C:/Users/<USER>/.deno/bin/compute-sanitizer.exe"
      - "C:/Users/<USER>/.deno/bin/compute-sanitizer"
      - "C:/Users/<USER>/AppData/Roaming/npm/compute-sanitizer.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/compute-sanitizer.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/compute-sanitizer"
      - "C:/Program Files/bin/compute-sanitizer.com"
      - "C:/Program Files/bin/compute-sanitizer.exe"
      - "C:/Program Files/bin/compute-sanitizer"
      - "C:/Program Files/sbin/compute-sanitizer.com"
      - "C:/Program Files/sbin/compute-sanitizer.exe"
      - "C:/Program Files/sbin/compute-sanitizer"
      - "C:/Program Files/compute-sanitizer.com"
      - "C:/Program Files/compute-sanitizer.exe"
      - "C:/Program Files/compute-sanitizer"
      - "C:/Program Files (x86)/bin/compute-sanitizer.com"
      - "C:/Program Files (x86)/bin/compute-sanitizer.exe"
      - "C:/Program Files (x86)/bin/compute-sanitizer"
      - "C:/Program Files (x86)/sbin/compute-sanitizer.com"
      - "C:/Program Files (x86)/sbin/compute-sanitizer.exe"
      - "C:/Program Files (x86)/sbin/compute-sanitizer"
      - "C:/Program Files (x86)/compute-sanitizer.com"
      - "C:/Program Files (x86)/compute-sanitizer.exe"
      - "C:/Program Files (x86)/compute-sanitizer"
      - "C:/Program Files/CMake/bin/compute-sanitizer.com"
      - "C:/Program Files/CMake/bin/compute-sanitizer.exe"
      - "C:/Program Files/CMake/bin/compute-sanitizer"
      - "C:/Program Files/CMake/sbin/compute-sanitizer.com"
      - "C:/Program Files/CMake/sbin/compute-sanitizer.exe"
      - "C:/Program Files/CMake/sbin/compute-sanitizer"
      - "C:/Program Files/CMake/compute-sanitizer.com"
      - "C:/Program Files/CMake/compute-sanitizer.exe"
      - "C:/Program Files/CMake/compute-sanitizer"
      - "/REGISTRY-NOTFOUND/compute-sanitizer.com"
      - "/REGISTRY-NOTFOUND/compute-sanitizer.exe"
      - "/REGISTRY-NOTFOUND/compute-sanitizer"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\Program Files\\Java\\jdk-21\\bin"
        - "C:\\Program Files\\Common Files\\Oracle\\Java\\javapath"
        - "C:\\Program Files\\Microsoft\\jdk-*********-hotspot\\bin"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\ProgramData\\chocolatey\\bin"
        - "C:\\Program Files\\Microsoft VS Code\\bin"
        - "C:\\Program Files\\Open Steno Project\\Plover 5.0.0.dev1"
        - "C:\\Portable Applications"
        - "C:\\Portable Applications\\yt-dlp"
        - "C:\\Program Files\\Calibre2\\"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "C:\\Portable Applications\\zig"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\pnpm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\"
        - "C:\\Users\\<USER>\\.cargo\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\platform-tools"
        - "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\tools"
        - "C:\\Users\\<USER>\\AppData\\Local\\pnpm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Ollama"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Users\\<USER>\\.deno\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
      CMAKE_INSTALL_PREFIX: "C:/Program Files (x86)/."
      CMAKE_SYSTEM_PREFIX_PATH:
        - "C:/Program Files"
        - "C:/Program Files (x86)"
        - "C:/Program Files/CMake"
        - "C:/Program Files (x86)/."
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CTest.cmake:196 (find_program)"
      - "CMakeLists.txt:6 (include)"
    mode: "program"
    variable: "COVERAGE_COMMAND"
    description: "Path to the coverage program that CTest uses for performing coverage inspection"
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "gcov"
    candidate_directories:
      - "C:/Program Files/Java/jdk-21/bin/"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/ProgramData/chocolatey/bin/"
      - "C:/Program Files/Microsoft VS Code/bin/"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/"
      - "C:/Portable Applications/"
      - "C:/Portable Applications/yt-dlp/"
      - "C:/Program Files/Calibre2/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/Docker/Docker/resources/bin/"
      - "C:/Portable Applications/zig/"
      - "C:/Program Files/nodejs/"
      - "C:/Program Files/dotnet/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/"
      - "C:/Users/<USER>/AppData/Local/pnpm/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/"
      - "C:/Users/<USER>/.cargo/bin/"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Users/<USER>/.deno/bin/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "C:/Program Files/bin/"
      - "C:/Program Files/sbin/"
      - "C:/Program Files/"
      - "C:/Program Files (x86)/bin/"
      - "C:/Program Files (x86)/sbin/"
      - "C:/Program Files (x86)/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Program Files/CMake/sbin/"
      - "C:/Program Files/CMake/"
    searched_directories:
      - "C:/Program Files/Java/jdk-21/bin/gcov.com"
      - "C:/Program Files/Java/jdk-21/bin/gcov.exe"
      - "C:/Program Files/Java/jdk-21/bin/gcov"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/gcov.com"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/gcov.exe"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/gcov"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/gcov.com"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/gcov.exe"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/gcov"
      - "C:/Windows/System32/gcov.com"
      - "C:/Windows/System32/gcov.exe"
      - "C:/Windows/System32/gcov"
      - "C:/Windows/gcov.com"
      - "C:/Windows/gcov.exe"
      - "C:/Windows/gcov"
      - "C:/Windows/System32/wbem/gcov.com"
      - "C:/Windows/System32/wbem/gcov.exe"
      - "C:/Windows/System32/wbem/gcov"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcov.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcov.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcov"
      - "C:/Windows/System32/OpenSSH/gcov.com"
      - "C:/Windows/System32/OpenSSH/gcov.exe"
      - "C:/Windows/System32/OpenSSH/gcov"
      - "C:/ProgramData/chocolatey/bin/gcov.com"
      - "C:/ProgramData/chocolatey/bin/gcov.exe"
      - "C:/ProgramData/chocolatey/bin/gcov"
      - "C:/Program Files/Microsoft VS Code/bin/gcov.com"
      - "C:/Program Files/Microsoft VS Code/bin/gcov.exe"
      - "C:/Program Files/Microsoft VS Code/bin/gcov"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/gcov.com"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/gcov.exe"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/gcov"
      - "C:/Portable Applications/gcov.com"
      - "C:/Portable Applications/gcov.exe"
      - "C:/Portable Applications/gcov"
      - "C:/Portable Applications/yt-dlp/gcov.com"
      - "C:/Portable Applications/yt-dlp/gcov.exe"
      - "C:/Portable Applications/yt-dlp/gcov"
      - "C:/Program Files/Calibre2/gcov.com"
      - "C:/Program Files/Calibre2/gcov.exe"
      - "C:/Program Files/Calibre2/gcov"
      - "C:/Program Files/Git/cmd/gcov.com"
      - "C:/Program Files/Git/cmd/gcov.exe"
      - "C:/Program Files/Git/cmd/gcov"
      - "C:/Program Files/Docker/Docker/resources/bin/gcov.com"
      - "C:/Program Files/Docker/Docker/resources/bin/gcov.exe"
      - "C:/Program Files/Docker/Docker/resources/bin/gcov"
      - "C:/Portable Applications/zig/gcov.com"
      - "C:/Portable Applications/zig/gcov.exe"
      - "C:/Portable Applications/zig/gcov"
      - "C:/Program Files/nodejs/gcov.com"
      - "C:/Program Files/nodejs/gcov.exe"
      - "C:/Program Files/nodejs/gcov"
      - "C:/Program Files/dotnet/gcov.com"
      - "C:/Program Files/dotnet/gcov.exe"
      - "C:/Program Files/dotnet/gcov"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/gcov.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/gcov.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/gcov"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/gcov.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/gcov.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/gcov"
      - "C:/Users/<USER>/AppData/Local/pnpm/gcov.com"
      - "C:/Users/<USER>/AppData/Local/pnpm/gcov.exe"
      - "C:/Users/<USER>/AppData/Local/pnpm/gcov"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/gcov.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/gcov.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/gcov"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/gcov.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/gcov.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/gcov"
      - "C:/Users/<USER>/.cargo/bin/gcov.com"
      - "C:/Users/<USER>/.cargo/bin/gcov.exe"
      - "C:/Users/<USER>/.cargo/bin/gcov"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/gcov.com"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/gcov.exe"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/gcov"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/gcov.com"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/gcov.exe"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/gcov"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcov.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcov.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcov"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/gcov.com"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/gcov.exe"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/gcov"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/gcov.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/gcov.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/gcov"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/gcov.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/gcov.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/gcov"
      - "C:/Users/<USER>/.deno/bin/gcov.com"
      - "C:/Users/<USER>/.deno/bin/gcov.exe"
      - "C:/Users/<USER>/.deno/bin/gcov"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcov.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcov.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcov"
      - "C:/Program Files/bin/gcov.com"
      - "C:/Program Files/bin/gcov.exe"
      - "C:/Program Files/bin/gcov"
      - "C:/Program Files/sbin/gcov.com"
      - "C:/Program Files/sbin/gcov.exe"
      - "C:/Program Files/sbin/gcov"
      - "C:/Program Files/gcov.com"
      - "C:/Program Files/gcov.exe"
      - "C:/Program Files/gcov"
      - "C:/Program Files (x86)/bin/gcov.com"
      - "C:/Program Files (x86)/bin/gcov.exe"
      - "C:/Program Files (x86)/bin/gcov"
      - "C:/Program Files (x86)/sbin/gcov.com"
      - "C:/Program Files (x86)/sbin/gcov.exe"
      - "C:/Program Files (x86)/sbin/gcov"
      - "C:/Program Files (x86)/gcov.com"
      - "C:/Program Files (x86)/gcov.exe"
      - "C:/Program Files (x86)/gcov"
      - "C:/Program Files/CMake/bin/gcov.com"
      - "C:/Program Files/CMake/bin/gcov.exe"
      - "C:/Program Files/CMake/bin/gcov"
      - "C:/Program Files/CMake/sbin/gcov.com"
      - "C:/Program Files/CMake/sbin/gcov.exe"
      - "C:/Program Files/CMake/sbin/gcov"
      - "C:/Program Files/CMake/gcov.com"
      - "C:/Program Files/CMake/gcov.exe"
      - "C:/Program Files/CMake/gcov"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\Program Files\\Java\\jdk-21\\bin"
        - "C:\\Program Files\\Common Files\\Oracle\\Java\\javapath"
        - "C:\\Program Files\\Microsoft\\jdk-*********-hotspot\\bin"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\ProgramData\\chocolatey\\bin"
        - "C:\\Program Files\\Microsoft VS Code\\bin"
        - "C:\\Program Files\\Open Steno Project\\Plover 5.0.0.dev1"
        - "C:\\Portable Applications"
        - "C:\\Portable Applications\\yt-dlp"
        - "C:\\Program Files\\Calibre2\\"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "C:\\Portable Applications\\zig"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\pnpm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\"
        - "C:\\Users\\<USER>\\.cargo\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\platform-tools"
        - "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\tools"
        - "C:\\Users\\<USER>\\AppData\\Local\\pnpm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Ollama"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Users\\<USER>\\.deno\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
      CMAKE_INSTALL_PREFIX: "C:/Program Files (x86)/."
      CMAKE_SYSTEM_PREFIX_PATH:
        - "C:/Program Files"
        - "C:/Program Files (x86)"
        - "C:/Program Files/CMake"
        - "C:/Program Files (x86)/."
...

---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineSystem.cmake:212 (message)"
      - "CMakeLists.txt:2 (project)"
    message: |
      The system is: Windows - 10.0.26100 - AMD64
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:462 (find_file)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:500 (CMAKE_DETERMINE_COMPILER_ID_WRITE)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:8 (CMAKE_DETERMINE_COMPILER_ID_BUILD)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:122 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    mode: "file"
    variable: "src_in"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "CMakeCCompilerId.c.in"
    candidate_directories:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/"
    found: "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeCCompilerId.c.in"
    search_context:
      ENV{PATH}:
        - "C:\\Program Files\\Java\\jdk-21\\bin"
        - "C:\\Program Files\\Common Files\\Oracle\\Java\\javapath"
        - "C:\\Program Files\\Microsoft\\jdk-*********-hotspot\\bin"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\ProgramData\\chocolatey\\bin"
        - "C:\\Program Files\\Microsoft VS Code\\bin"
        - "C:\\Program Files\\Open Steno Project\\Plover 5.0.0.dev1"
        - "C:\\Portable Applications"
        - "C:\\Portable Applications\\yt-dlp"
        - "C:\\Program Files\\Calibre2\\"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "C:\\Portable Applications\\zig"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\pnpm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\"
        - "C:\\Users\\<USER>\\.cargo\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\platform-tools"
        - "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\tools"
        - "C:\\Users\\<USER>\\AppData\\Local\\pnpm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Ollama"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Users\\<USER>\\.deno\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:122 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      MSBuild version 17.13.19+0d9f5a35a for .NET Framework
      Build started 7/10/2025 6:03:07 PM.
      
      Project "C:\\Users\\<USER>\\_Items\\Dev\\GitHub\\scale\\aider-quality-cpp\\build\\CMakeFiles\\4.1.0-rc1\\CompilerIdC\\CompilerIdC.vcxproj" on node 1 (default targets).
      PrepareForBuild:
        Creating directory "Debug\\".
        Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
        Creating directory "Debug\\CompilerIdC.tlog\\".
      InitializeBuildStatus:
        Creating "Debug\\CompilerIdC.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
        Touching "Debug\\CompilerIdC.tlog\\unsuccessfulbuild".
      ClCompile:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\CL.exe /c /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc143.pdb" /external:W0 /Gd /TC /FC /errorReport:queue CMakeCCompilerId.c
        CMakeCCompilerId.c
      Link:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdC.exe" /INCREMENTAL:NO /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdC.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdC.lib" /MACHINE:X64 Debug\\CMakeCCompilerId.obj
        CompilerIdC.vcxproj -> C:\\Users\\<USER>\\_Items\\Dev\\GitHub\\scale\\aider-quality-cpp\\build\\CMakeFiles\\4.1.0-rc1\\CompilerIdC\\CompilerIdC.exe
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_C_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_C_COMPILER=C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\Hostx64\\x64\\cl.exe
      FinalizeBuildStatus:
        Deleting file "Debug\\CompilerIdC.tlog\\unsuccessfulbuild".
        Touching "Debug\\CompilerIdC.tlog\\CompilerIdC.lastbuildstate".
      Done Building Project "C:\\Users\\<USER>\\_Items\\Dev\\GitHub\\scale\\aider-quality-cpp\\build\\CMakeFiles\\4.1.0-rc1\\CompilerIdC\\CompilerIdC.vcxproj" (default targets).
      
      Build succeeded.
          0 Warning(s)
          0 Error(s)
      
      Time Elapsed 00:00:01.49
      
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CompilerIdC.exe"
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CompilerIdC.vcxproj"
      
      The C compiler identification is MSVC, found in:
        C:/Users/<USER>/_Items/Dev/GitHub/scale/aider-quality-cpp/build/CMakeFiles/4.1.0-rc1/CompilerIdC/CompilerIdC.exe
      
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:37 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:65 (__resolve_tool_path)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:103 (__resolve_linker_path)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "_CMAKE_TOOL_WITH_PATH"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "link"
    candidate_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/"
      - "C:/Program Files/Java/jdk-21/bin/"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/ProgramData/chocolatey/bin/"
      - "C:/Program Files/Microsoft VS Code/bin/"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/"
      - "C:/Portable Applications/"
      - "C:/Portable Applications/yt-dlp/"
      - "C:/Program Files/Calibre2/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/Docker/Docker/resources/bin/"
      - "C:/Portable Applications/zig/"
      - "C:/Program Files/nodejs/"
      - "C:/Program Files/dotnet/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/"
      - "C:/Users/<USER>/AppData/Local/pnpm/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/"
      - "C:/Users/<USER>/.cargo/bin/"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Users/<USER>/.deno/bin/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
    searched_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/link.com"
    found: "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/link.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Program Files\\Java\\jdk-21\\bin"
        - "C:\\Program Files\\Common Files\\Oracle\\Java\\javapath"
        - "C:\\Program Files\\Microsoft\\jdk-*********-hotspot\\bin"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\ProgramData\\chocolatey\\bin"
        - "C:\\Program Files\\Microsoft VS Code\\bin"
        - "C:\\Program Files\\Open Steno Project\\Plover 5.0.0.dev1"
        - "C:\\Portable Applications"
        - "C:\\Portable Applications\\yt-dlp"
        - "C:\\Program Files\\Calibre2\\"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "C:\\Portable Applications\\zig"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\pnpm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\"
        - "C:\\Users\\<USER>\\.cargo\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\platform-tools"
        - "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\tools"
        - "C:\\Users\\<USER>\\AppData\\Local\\pnpm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Ollama"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Users\\<USER>\\.deno\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:37 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:65 (__resolve_tool_path)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:104 (__resolve_linker_path)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "_CMAKE_TOOL_WITH_PATH"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "lld-link"
    candidate_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/"
      - "C:/Program Files/Java/jdk-21/bin/"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/ProgramData/chocolatey/bin/"
      - "C:/Program Files/Microsoft VS Code/bin/"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/"
      - "C:/Portable Applications/"
      - "C:/Portable Applications/yt-dlp/"
      - "C:/Program Files/Calibre2/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/Docker/Docker/resources/bin/"
      - "C:/Portable Applications/zig/"
      - "C:/Program Files/nodejs/"
      - "C:/Program Files/dotnet/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/"
      - "C:/Users/<USER>/AppData/Local/pnpm/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/"
      - "C:/Users/<USER>/.cargo/bin/"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Users/<USER>/.deno/bin/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
    searched_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/lld-link.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/lld-link.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/lld-link"
      - "C:/Program Files/Java/jdk-21/bin/lld-link.com"
      - "C:/Program Files/Java/jdk-21/bin/lld-link.exe"
      - "C:/Program Files/Java/jdk-21/bin/lld-link"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/lld-link.com"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/lld-link.exe"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/lld-link"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/lld-link.com"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/lld-link.exe"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/lld-link"
      - "C:/Windows/System32/lld-link.com"
      - "C:/Windows/System32/lld-link.exe"
      - "C:/Windows/System32/lld-link"
      - "C:/Windows/lld-link.com"
      - "C:/Windows/lld-link.exe"
      - "C:/Windows/lld-link"
      - "C:/Windows/System32/wbem/lld-link.com"
      - "C:/Windows/System32/wbem/lld-link.exe"
      - "C:/Windows/System32/wbem/lld-link"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/lld-link.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/lld-link.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/lld-link"
      - "C:/Windows/System32/OpenSSH/lld-link.com"
      - "C:/Windows/System32/OpenSSH/lld-link.exe"
      - "C:/Windows/System32/OpenSSH/lld-link"
      - "C:/ProgramData/chocolatey/bin/lld-link.com"
      - "C:/ProgramData/chocolatey/bin/lld-link.exe"
      - "C:/ProgramData/chocolatey/bin/lld-link"
      - "C:/Program Files/Microsoft VS Code/bin/lld-link.com"
      - "C:/Program Files/Microsoft VS Code/bin/lld-link.exe"
      - "C:/Program Files/Microsoft VS Code/bin/lld-link"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/lld-link.com"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/lld-link.exe"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/lld-link"
      - "C:/Portable Applications/lld-link.com"
      - "C:/Portable Applications/lld-link.exe"
      - "C:/Portable Applications/lld-link"
      - "C:/Portable Applications/yt-dlp/lld-link.com"
      - "C:/Portable Applications/yt-dlp/lld-link.exe"
      - "C:/Portable Applications/yt-dlp/lld-link"
      - "C:/Program Files/Calibre2/lld-link.com"
      - "C:/Program Files/Calibre2/lld-link.exe"
      - "C:/Program Files/Calibre2/lld-link"
      - "C:/Program Files/Git/cmd/lld-link.com"
      - "C:/Program Files/Git/cmd/lld-link.exe"
      - "C:/Program Files/Git/cmd/lld-link"
      - "C:/Program Files/Docker/Docker/resources/bin/lld-link.com"
      - "C:/Program Files/Docker/Docker/resources/bin/lld-link.exe"
      - "C:/Program Files/Docker/Docker/resources/bin/lld-link"
      - "C:/Portable Applications/zig/lld-link.com"
      - "C:/Portable Applications/zig/lld-link.exe"
      - "C:/Portable Applications/zig/lld-link"
      - "C:/Program Files/nodejs/lld-link.com"
      - "C:/Program Files/nodejs/lld-link.exe"
      - "C:/Program Files/nodejs/lld-link"
      - "C:/Program Files/dotnet/lld-link.com"
      - "C:/Program Files/dotnet/lld-link.exe"
      - "C:/Program Files/dotnet/lld-link"
      - "C:/Program Files/CMake/bin/lld-link.com"
      - "C:/Program Files/CMake/bin/lld-link.exe"
      - "C:/Program Files/CMake/bin/lld-link"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/lld-link"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/lld-link"
      - "C:/Users/<USER>/AppData/Local/pnpm/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/pnpm/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/pnpm/lld-link"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/lld-link"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/lld-link"
      - "C:/Users/<USER>/.cargo/bin/lld-link.com"
      - "C:/Users/<USER>/.cargo/bin/lld-link.exe"
      - "C:/Users/<USER>/.cargo/bin/lld-link"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/lld-link"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/lld-link"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/lld-link"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/lld-link"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/lld-link"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/lld-link"
      - "C:/Users/<USER>/.deno/bin/lld-link.com"
      - "C:/Users/<USER>/.deno/bin/lld-link.exe"
      - "C:/Users/<USER>/.deno/bin/lld-link"
      - "C:/Users/<USER>/AppData/Roaming/npm/lld-link.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/lld-link.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/lld-link"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\Program Files\\Java\\jdk-21\\bin"
        - "C:\\Program Files\\Common Files\\Oracle\\Java\\javapath"
        - "C:\\Program Files\\Microsoft\\jdk-*********-hotspot\\bin"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\ProgramData\\chocolatey\\bin"
        - "C:\\Program Files\\Microsoft VS Code\\bin"
        - "C:\\Program Files\\Open Steno Project\\Plover 5.0.0.dev1"
        - "C:\\Portable Applications"
        - "C:\\Portable Applications\\yt-dlp"
        - "C:\\Program Files\\Calibre2\\"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "C:\\Portable Applications\\zig"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\pnpm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\"
        - "C:\\Users\\<USER>\\.cargo\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\platform-tools"
        - "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\tools"
        - "C:\\Users\\<USER>\\AppData\\Local\\pnpm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Ollama"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Users\\<USER>\\.deno\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_LINKER"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "link"
    candidate_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/"
      - "C:/Program Files/Java/jdk-21/bin/"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/ProgramData/chocolatey/bin/"
      - "C:/Program Files/Microsoft VS Code/bin/"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/"
      - "C:/Portable Applications/"
      - "C:/Portable Applications/yt-dlp/"
      - "C:/Program Files/Calibre2/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/Docker/Docker/resources/bin/"
      - "C:/Portable Applications/zig/"
      - "C:/Program Files/nodejs/"
      - "C:/Program Files/dotnet/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/"
      - "C:/Users/<USER>/AppData/Local/pnpm/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/"
      - "C:/Users/<USER>/.cargo/bin/"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Users/<USER>/.deno/bin/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
    searched_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/link.com"
    found: "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/link.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Program Files\\Java\\jdk-21\\bin"
        - "C:\\Program Files\\Common Files\\Oracle\\Java\\javapath"
        - "C:\\Program Files\\Microsoft\\jdk-*********-hotspot\\bin"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\ProgramData\\chocolatey\\bin"
        - "C:\\Program Files\\Microsoft VS Code\\bin"
        - "C:\\Program Files\\Open Steno Project\\Plover 5.0.0.dev1"
        - "C:\\Portable Applications"
        - "C:\\Portable Applications\\yt-dlp"
        - "C:\\Program Files\\Calibre2\\"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "C:\\Portable Applications\\zig"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\pnpm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\"
        - "C:\\Users\\<USER>\\.cargo\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\platform-tools"
        - "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\tools"
        - "C:\\Users\\<USER>\\AppData\\Local\\pnpm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Ollama"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Users\\<USER>\\.deno\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_MT"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "mt"
    candidate_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/"
      - "C:/Program Files/Java/jdk-21/bin/"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/ProgramData/chocolatey/bin/"
      - "C:/Program Files/Microsoft VS Code/bin/"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/"
      - "C:/Portable Applications/"
      - "C:/Portable Applications/yt-dlp/"
      - "C:/Program Files/Calibre2/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/Docker/Docker/resources/bin/"
      - "C:/Portable Applications/zig/"
      - "C:/Program Files/nodejs/"
      - "C:/Program Files/dotnet/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/"
      - "C:/Users/<USER>/AppData/Local/pnpm/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/"
      - "C:/Users/<USER>/.cargo/bin/"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Users/<USER>/.deno/bin/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
    searched_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/mt.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/mt.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/mt"
      - "C:/Program Files/Java/jdk-21/bin/mt.com"
      - "C:/Program Files/Java/jdk-21/bin/mt.exe"
      - "C:/Program Files/Java/jdk-21/bin/mt"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/mt.com"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/mt.exe"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/mt"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/mt.com"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/mt.exe"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/mt"
      - "C:/Windows/System32/mt.com"
      - "C:/Windows/System32/mt.exe"
      - "C:/Windows/System32/mt"
      - "C:/Windows/mt.com"
      - "C:/Windows/mt.exe"
      - "C:/Windows/mt"
      - "C:/Windows/System32/wbem/mt.com"
      - "C:/Windows/System32/wbem/mt.exe"
      - "C:/Windows/System32/wbem/mt"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/mt.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/mt.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/mt"
      - "C:/Windows/System32/OpenSSH/mt.com"
      - "C:/Windows/System32/OpenSSH/mt.exe"
      - "C:/Windows/System32/OpenSSH/mt"
      - "C:/ProgramData/chocolatey/bin/mt.com"
      - "C:/ProgramData/chocolatey/bin/mt.exe"
      - "C:/ProgramData/chocolatey/bin/mt"
      - "C:/Program Files/Microsoft VS Code/bin/mt.com"
      - "C:/Program Files/Microsoft VS Code/bin/mt.exe"
      - "C:/Program Files/Microsoft VS Code/bin/mt"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/mt.com"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/mt.exe"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/mt"
      - "C:/Portable Applications/mt.com"
      - "C:/Portable Applications/mt.exe"
      - "C:/Portable Applications/mt"
      - "C:/Portable Applications/yt-dlp/mt.com"
      - "C:/Portable Applications/yt-dlp/mt.exe"
      - "C:/Portable Applications/yt-dlp/mt"
      - "C:/Program Files/Calibre2/mt.com"
      - "C:/Program Files/Calibre2/mt.exe"
      - "C:/Program Files/Calibre2/mt"
      - "C:/Program Files/Git/cmd/mt.com"
      - "C:/Program Files/Git/cmd/mt.exe"
      - "C:/Program Files/Git/cmd/mt"
      - "C:/Program Files/Docker/Docker/resources/bin/mt.com"
      - "C:/Program Files/Docker/Docker/resources/bin/mt.exe"
      - "C:/Program Files/Docker/Docker/resources/bin/mt"
      - "C:/Portable Applications/zig/mt.com"
      - "C:/Portable Applications/zig/mt.exe"
      - "C:/Portable Applications/zig/mt"
      - "C:/Program Files/nodejs/mt.com"
      - "C:/Program Files/nodejs/mt.exe"
      - "C:/Program Files/nodejs/mt"
      - "C:/Program Files/dotnet/mt.com"
      - "C:/Program Files/dotnet/mt.exe"
      - "C:/Program Files/dotnet/mt"
      - "C:/Program Files/CMake/bin/mt.com"
      - "C:/Program Files/CMake/bin/mt.exe"
      - "C:/Program Files/CMake/bin/mt"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/mt.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/mt.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/mt"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/mt.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/mt.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/mt"
      - "C:/Users/<USER>/AppData/Local/pnpm/mt.com"
      - "C:/Users/<USER>/AppData/Local/pnpm/mt.exe"
      - "C:/Users/<USER>/AppData/Local/pnpm/mt"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/mt.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/mt.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/mt"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/mt.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/mt.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/mt"
      - "C:/Users/<USER>/.cargo/bin/mt.com"
      - "C:/Users/<USER>/.cargo/bin/mt.exe"
      - "C:/Users/<USER>/.cargo/bin/mt"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/mt.com"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/mt.exe"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/mt"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/mt.com"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/mt.exe"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/mt"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/mt.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/mt.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/mt"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/mt.com"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/mt.exe"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/mt"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/mt.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/mt.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/mt"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/mt.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/mt.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/mt"
      - "C:/Users/<USER>/.deno/bin/mt.com"
      - "C:/Users/<USER>/.deno/bin/mt.exe"
      - "C:/Users/<USER>/.deno/bin/mt"
      - "C:/Users/<USER>/AppData/Roaming/npm/mt.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/mt.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/mt"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\Program Files\\Java\\jdk-21\\bin"
        - "C:\\Program Files\\Common Files\\Oracle\\Java\\javapath"
        - "C:\\Program Files\\Microsoft\\jdk-*********-hotspot\\bin"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\ProgramData\\chocolatey\\bin"
        - "C:\\Program Files\\Microsoft VS Code\\bin"
        - "C:\\Program Files\\Open Steno Project\\Plover 5.0.0.dev1"
        - "C:\\Portable Applications"
        - "C:\\Portable Applications\\yt-dlp"
        - "C:\\Program Files\\Calibre2\\"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "C:\\Portable Applications\\zig"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\pnpm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\"
        - "C:\\Users\\<USER>\\.cargo\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\platform-tools"
        - "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\tools"
        - "C:\\Users\\<USER>\\AppData\\Local\\pnpm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Ollama"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Users\\<USER>\\.deno\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_AR"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "lib"
    candidate_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/"
      - "C:/Program Files/Java/jdk-21/bin/"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/ProgramData/chocolatey/bin/"
      - "C:/Program Files/Microsoft VS Code/bin/"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/"
      - "C:/Portable Applications/"
      - "C:/Portable Applications/yt-dlp/"
      - "C:/Program Files/Calibre2/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/Docker/Docker/resources/bin/"
      - "C:/Portable Applications/zig/"
      - "C:/Program Files/nodejs/"
      - "C:/Program Files/dotnet/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/"
      - "C:/Users/<USER>/AppData/Local/pnpm/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/"
      - "C:/Users/<USER>/.cargo/bin/"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Users/<USER>/.deno/bin/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
    searched_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/lib.com"
    found: "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/lib.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Program Files\\Java\\jdk-21\\bin"
        - "C:\\Program Files\\Common Files\\Oracle\\Java\\javapath"
        - "C:\\Program Files\\Microsoft\\jdk-*********-hotspot\\bin"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\ProgramData\\chocolatey\\bin"
        - "C:\\Program Files\\Microsoft VS Code\\bin"
        - "C:\\Program Files\\Open Steno Project\\Plover 5.0.0.dev1"
        - "C:\\Portable Applications"
        - "C:\\Portable Applications\\yt-dlp"
        - "C:\\Program Files\\Calibre2\\"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "C:\\Portable Applications\\zig"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\pnpm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\"
        - "C:\\Users\\<USER>\\.cargo\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\platform-tools"
        - "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\tools"
        - "C:\\Users\\<USER>\\AppData\\Local\\pnpm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Ollama"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Users\\<USER>\\.deno\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:462 (find_file)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:500 (CMAKE_DETERMINE_COMPILER_ID_WRITE)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:8 (CMAKE_DETERMINE_COMPILER_ID_BUILD)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    mode: "file"
    variable: "src_in"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "CMakeCXXCompilerId.cpp.in"
    candidate_directories:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/"
    found: "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeCXXCompilerId.cpp.in"
    search_context:
      ENV{PATH}:
        - "C:\\Program Files\\Java\\jdk-21\\bin"
        - "C:\\Program Files\\Common Files\\Oracle\\Java\\javapath"
        - "C:\\Program Files\\Microsoft\\jdk-*********-hotspot\\bin"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\ProgramData\\chocolatey\\bin"
        - "C:\\Program Files\\Microsoft VS Code\\bin"
        - "C:\\Program Files\\Open Steno Project\\Plover 5.0.0.dev1"
        - "C:\\Portable Applications"
        - "C:\\Portable Applications\\yt-dlp"
        - "C:\\Program Files\\Calibre2\\"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "C:\\Portable Applications\\zig"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\pnpm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\"
        - "C:\\Users\\<USER>\\.cargo\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\platform-tools"
        - "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\tools"
        - "C:\\Users\\<USER>\\AppData\\Local\\pnpm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Ollama"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Users\\<USER>\\.deno\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      MSBuild version 17.13.19+0d9f5a35a for .NET Framework
      Build started 7/10/2025 6:03:09 PM.
      
      Project "C:\\Users\\<USER>\\_Items\\Dev\\GitHub\\scale\\aider-quality-cpp\\build\\CMakeFiles\\4.1.0-rc1\\CompilerIdCXX\\CompilerIdCXX.vcxproj" on node 1 (default targets).
      PrepareForBuild:
        Creating directory "Debug\\".
        Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
        Creating directory "Debug\\CompilerIdCXX.tlog\\".
      InitializeBuildStatus:
        Creating "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
        Touching "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild".
      ClCompile:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\CL.exe /c /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc143.pdb" /external:W0 /Gd /TP /FC /errorReport:queue CMakeCXXCompilerId.cpp
        CMakeCXXCompilerId.cpp
      Link:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdCXX.exe" /INCREMENTAL:NO /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdCXX.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdCXX.lib" /MACHINE:X64 Debug\\CMakeCXXCompilerId.obj
        CompilerIdCXX.vcxproj -> C:\\Users\\<USER>\\_Items\\Dev\\GitHub\\scale\\aider-quality-cpp\\build\\CMakeFiles\\4.1.0-rc1\\CompilerIdCXX\\CompilerIdCXX.exe
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_CXX_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_CXX_COMPILER=C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\Hostx64\\x64\\cl.exe
      FinalizeBuildStatus:
        Deleting file "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild".
        Touching "Debug\\CompilerIdCXX.tlog\\CompilerIdCXX.lastbuildstate".
      Done Building Project "C:\\Users\\<USER>\\_Items\\Dev\\GitHub\\scale\\aider-quality-cpp\\build\\CMakeFiles\\4.1.0-rc1\\CompilerIdCXX\\CompilerIdCXX.vcxproj" (default targets).
      
      Build succeeded.
          0 Warning(s)
          0 Error(s)
      
      Time Elapsed 00:00:01.12
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.exe"
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.vcxproj"
      
      The CXX compiler identification is MSVC, found in:
        C:/Users/<USER>/_Items/Dev/GitHub/scale/aider-quality-cpp/build/CMakeFiles/4.1.0-rc1/CompilerIdCXX/CompilerIdCXX.exe
      
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:37 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:65 (__resolve_tool_path)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:104 (__resolve_linker_path)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:206 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "_CMAKE_TOOL_WITH_PATH"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "lld-link"
    candidate_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/"
      - "C:/Program Files/Java/jdk-21/bin/"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/ProgramData/chocolatey/bin/"
      - "C:/Program Files/Microsoft VS Code/bin/"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/"
      - "C:/Portable Applications/"
      - "C:/Portable Applications/yt-dlp/"
      - "C:/Program Files/Calibre2/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/Docker/Docker/resources/bin/"
      - "C:/Portable Applications/zig/"
      - "C:/Program Files/nodejs/"
      - "C:/Program Files/dotnet/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/"
      - "C:/Users/<USER>/AppData/Local/pnpm/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/"
      - "C:/Users/<USER>/.cargo/bin/"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Users/<USER>/.deno/bin/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
    searched_directories:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/lld-link.com"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/lld-link.exe"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/lld-link"
      - "C:/Program Files/Java/jdk-21/bin/lld-link.com"
      - "C:/Program Files/Java/jdk-21/bin/lld-link.exe"
      - "C:/Program Files/Java/jdk-21/bin/lld-link"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/lld-link.com"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/lld-link.exe"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/lld-link"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/lld-link.com"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/lld-link.exe"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/lld-link"
      - "C:/Windows/System32/lld-link.com"
      - "C:/Windows/System32/lld-link.exe"
      - "C:/Windows/System32/lld-link"
      - "C:/Windows/lld-link.com"
      - "C:/Windows/lld-link.exe"
      - "C:/Windows/lld-link"
      - "C:/Windows/System32/wbem/lld-link.com"
      - "C:/Windows/System32/wbem/lld-link.exe"
      - "C:/Windows/System32/wbem/lld-link"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/lld-link.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/lld-link.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/lld-link"
      - "C:/Windows/System32/OpenSSH/lld-link.com"
      - "C:/Windows/System32/OpenSSH/lld-link.exe"
      - "C:/Windows/System32/OpenSSH/lld-link"
      - "C:/ProgramData/chocolatey/bin/lld-link.com"
      - "C:/ProgramData/chocolatey/bin/lld-link.exe"
      - "C:/ProgramData/chocolatey/bin/lld-link"
      - "C:/Program Files/Microsoft VS Code/bin/lld-link.com"
      - "C:/Program Files/Microsoft VS Code/bin/lld-link.exe"
      - "C:/Program Files/Microsoft VS Code/bin/lld-link"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/lld-link.com"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/lld-link.exe"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/lld-link"
      - "C:/Portable Applications/lld-link.com"
      - "C:/Portable Applications/lld-link.exe"
      - "C:/Portable Applications/lld-link"
      - "C:/Portable Applications/yt-dlp/lld-link.com"
      - "C:/Portable Applications/yt-dlp/lld-link.exe"
      - "C:/Portable Applications/yt-dlp/lld-link"
      - "C:/Program Files/Calibre2/lld-link.com"
      - "C:/Program Files/Calibre2/lld-link.exe"
      - "C:/Program Files/Calibre2/lld-link"
      - "C:/Program Files/Git/cmd/lld-link.com"
      - "C:/Program Files/Git/cmd/lld-link.exe"
      - "C:/Program Files/Git/cmd/lld-link"
      - "C:/Program Files/Docker/Docker/resources/bin/lld-link.com"
      - "C:/Program Files/Docker/Docker/resources/bin/lld-link.exe"
      - "C:/Program Files/Docker/Docker/resources/bin/lld-link"
      - "C:/Portable Applications/zig/lld-link.com"
      - "C:/Portable Applications/zig/lld-link.exe"
      - "C:/Portable Applications/zig/lld-link"
      - "C:/Program Files/nodejs/lld-link.com"
      - "C:/Program Files/nodejs/lld-link.exe"
      - "C:/Program Files/nodejs/lld-link"
      - "C:/Program Files/dotnet/lld-link.com"
      - "C:/Program Files/dotnet/lld-link.exe"
      - "C:/Program Files/dotnet/lld-link"
      - "C:/Program Files/CMake/bin/lld-link.com"
      - "C:/Program Files/CMake/bin/lld-link.exe"
      - "C:/Program Files/CMake/bin/lld-link"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/lld-link"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/lld-link"
      - "C:/Users/<USER>/AppData/Local/pnpm/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/pnpm/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/pnpm/lld-link"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/lld-link"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/lld-link"
      - "C:/Users/<USER>/.cargo/bin/lld-link.com"
      - "C:/Users/<USER>/.cargo/bin/lld-link.exe"
      - "C:/Users/<USER>/.cargo/bin/lld-link"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/lld-link"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/lld-link"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/lld-link"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/lld-link"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/lld-link"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/lld-link"
      - "C:/Users/<USER>/.deno/bin/lld-link.com"
      - "C:/Users/<USER>/.deno/bin/lld-link.exe"
      - "C:/Users/<USER>/.deno/bin/lld-link"
      - "C:/Users/<USER>/AppData/Roaming/npm/lld-link.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/lld-link.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/lld-link"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\Program Files\\Java\\jdk-21\\bin"
        - "C:\\Program Files\\Common Files\\Oracle\\Java\\javapath"
        - "C:\\Program Files\\Microsoft\\jdk-*********-hotspot\\bin"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\ProgramData\\chocolatey\\bin"
        - "C:\\Program Files\\Microsoft VS Code\\bin"
        - "C:\\Program Files\\Open Steno Project\\Plover 5.0.0.dev1"
        - "C:\\Portable Applications"
        - "C:\\Portable Applications\\yt-dlp"
        - "C:\\Program Files\\Calibre2\\"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "C:\\Portable Applications\\zig"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\pnpm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\"
        - "C:\\Users\\<USER>\\.cargo\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\platform-tools"
        - "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\tools"
        - "C:\\Users\\<USER>\\AppData\\Local\\pnpm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Ollama"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Users\\<USER>\\.deno\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineRCCompiler.cmake:40 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Platform/Windows-MSVC.cmake:573 (enable_language)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Platform/Windows-MSVC.cmake:546 (__windows_compiler_msvc_enable_rc)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Platform/Windows-MSVC-C.cmake:5 (__windows_compiler_msvc)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeCInformation.cmake:48 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_RC_COMPILER"
    description: "RC compiler"
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "rc"
    candidate_directories:
      - "C:/Program Files/Java/jdk-21/bin/"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/ProgramData/chocolatey/bin/"
      - "C:/Program Files/Microsoft VS Code/bin/"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/"
      - "C:/Portable Applications/"
      - "C:/Portable Applications/yt-dlp/"
      - "C:/Program Files/Calibre2/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/Docker/Docker/resources/bin/"
      - "C:/Portable Applications/zig/"
      - "C:/Program Files/nodejs/"
      - "C:/Program Files/dotnet/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/"
      - "C:/Users/<USER>/AppData/Local/pnpm/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/"
      - "C:/Users/<USER>/.cargo/bin/"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Users/<USER>/.deno/bin/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "C:/Program Files/bin/"
      - "C:/Program Files/sbin/"
      - "C:/Program Files/"
      - "C:/Program Files (x86)/bin/"
      - "C:/Program Files (x86)/sbin/"
      - "C:/Program Files (x86)/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Program Files/CMake/sbin/"
      - "C:/Program Files/CMake/"
      - "C:/Program Files (x86)/aider-quality-cpp/bin/"
      - "C:/Program Files (x86)/aider-quality-cpp/sbin/"
      - "C:/Program Files (x86)/aider-quality-cpp/"
    searched_directories:
      - "C:/Program Files/Java/jdk-21/bin/rc.com"
      - "C:/Program Files/Java/jdk-21/bin/rc.exe"
      - "C:/Program Files/Java/jdk-21/bin/rc"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/rc.com"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/rc.exe"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/rc"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/rc.com"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/rc.exe"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/rc"
      - "C:/Windows/System32/rc.com"
      - "C:/Windows/System32/rc.exe"
      - "C:/Windows/System32/rc"
      - "C:/Windows/rc.com"
      - "C:/Windows/rc.exe"
      - "C:/Windows/rc"
      - "C:/Windows/System32/wbem/rc.com"
      - "C:/Windows/System32/wbem/rc.exe"
      - "C:/Windows/System32/wbem/rc"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/rc.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/rc.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/rc"
      - "C:/Windows/System32/OpenSSH/rc.com"
      - "C:/Windows/System32/OpenSSH/rc.exe"
      - "C:/Windows/System32/OpenSSH/rc"
      - "C:/ProgramData/chocolatey/bin/rc.com"
      - "C:/ProgramData/chocolatey/bin/rc.exe"
      - "C:/ProgramData/chocolatey/bin/rc"
      - "C:/Program Files/Microsoft VS Code/bin/rc.com"
      - "C:/Program Files/Microsoft VS Code/bin/rc.exe"
      - "C:/Program Files/Microsoft VS Code/bin/rc"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/rc.com"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/rc.exe"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/rc"
      - "C:/Portable Applications/rc.com"
      - "C:/Portable Applications/rc.exe"
      - "C:/Portable Applications/rc"
      - "C:/Portable Applications/yt-dlp/rc.com"
      - "C:/Portable Applications/yt-dlp/rc.exe"
      - "C:/Portable Applications/yt-dlp/rc"
      - "C:/Program Files/Calibre2/rc.com"
      - "C:/Program Files/Calibre2/rc.exe"
      - "C:/Program Files/Calibre2/rc"
      - "C:/Program Files/Git/cmd/rc.com"
      - "C:/Program Files/Git/cmd/rc.exe"
      - "C:/Program Files/Git/cmd/rc"
      - "C:/Program Files/Docker/Docker/resources/bin/rc.com"
      - "C:/Program Files/Docker/Docker/resources/bin/rc.exe"
      - "C:/Program Files/Docker/Docker/resources/bin/rc"
      - "C:/Portable Applications/zig/rc.com"
      - "C:/Portable Applications/zig/rc.exe"
      - "C:/Portable Applications/zig/rc"
      - "C:/Program Files/nodejs/rc.com"
      - "C:/Program Files/nodejs/rc.exe"
      - "C:/Program Files/nodejs/rc"
      - "C:/Program Files/dotnet/rc.com"
      - "C:/Program Files/dotnet/rc.exe"
      - "C:/Program Files/dotnet/rc"
      - "C:/Program Files/CMake/bin/rc.com"
      - "C:/Program Files/CMake/bin/rc.exe"
      - "C:/Program Files/CMake/bin/rc"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/rc.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/rc.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/rc"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/rc.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/rc.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/rc"
      - "C:/Users/<USER>/AppData/Local/pnpm/rc.com"
      - "C:/Users/<USER>/AppData/Local/pnpm/rc.exe"
      - "C:/Users/<USER>/AppData/Local/pnpm/rc"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/rc.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/rc.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/rc"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/rc.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/rc.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/rc"
      - "C:/Users/<USER>/.cargo/bin/rc.com"
      - "C:/Users/<USER>/.cargo/bin/rc.exe"
      - "C:/Users/<USER>/.cargo/bin/rc"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/rc.com"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/rc.exe"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/rc"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/rc.com"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/rc.exe"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/rc"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/rc.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/rc.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/rc"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/rc.com"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/rc.exe"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/rc"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/rc.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/rc.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/rc"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/rc.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/rc.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/rc"
      - "C:/Users/<USER>/.deno/bin/rc.com"
      - "C:/Users/<USER>/.deno/bin/rc.exe"
      - "C:/Users/<USER>/.deno/bin/rc"
      - "C:/Users/<USER>/AppData/Roaming/npm/rc.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/rc.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/rc"
      - "C:/Program Files/bin/rc.com"
      - "C:/Program Files/bin/rc.exe"
      - "C:/Program Files/bin/rc"
      - "C:/Program Files/sbin/rc.com"
      - "C:/Program Files/sbin/rc.exe"
      - "C:/Program Files/sbin/rc"
      - "C:/Program Files/rc.com"
      - "C:/Program Files/rc.exe"
      - "C:/Program Files/rc"
      - "C:/Program Files (x86)/bin/rc.com"
      - "C:/Program Files (x86)/bin/rc.exe"
      - "C:/Program Files (x86)/bin/rc"
      - "C:/Program Files (x86)/sbin/rc.com"
      - "C:/Program Files (x86)/sbin/rc.exe"
      - "C:/Program Files (x86)/sbin/rc"
      - "C:/Program Files (x86)/rc.com"
      - "C:/Program Files (x86)/rc.exe"
      - "C:/Program Files (x86)/rc"
      - "C:/Program Files/CMake/bin/rc.com"
      - "C:/Program Files/CMake/bin/rc.exe"
      - "C:/Program Files/CMake/bin/rc"
      - "C:/Program Files/CMake/sbin/rc.com"
      - "C:/Program Files/CMake/sbin/rc.exe"
      - "C:/Program Files/CMake/sbin/rc"
      - "C:/Program Files/CMake/rc.com"
      - "C:/Program Files/CMake/rc.exe"
      - "C:/Program Files/CMake/rc"
      - "C:/Program Files (x86)/aider-quality-cpp/bin/rc.com"
      - "C:/Program Files (x86)/aider-quality-cpp/bin/rc.exe"
      - "C:/Program Files (x86)/aider-quality-cpp/bin/rc"
      - "C:/Program Files (x86)/aider-quality-cpp/sbin/rc.com"
      - "C:/Program Files (x86)/aider-quality-cpp/sbin/rc.exe"
      - "C:/Program Files (x86)/aider-quality-cpp/sbin/rc"
      - "C:/Program Files (x86)/aider-quality-cpp/rc.com"
      - "C:/Program Files (x86)/aider-quality-cpp/rc.exe"
      - "C:/Program Files (x86)/aider-quality-cpp/rc"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\Program Files\\Java\\jdk-21\\bin"
        - "C:\\Program Files\\Common Files\\Oracle\\Java\\javapath"
        - "C:\\Program Files\\Microsoft\\jdk-*********-hotspot\\bin"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\ProgramData\\chocolatey\\bin"
        - "C:\\Program Files\\Microsoft VS Code\\bin"
        - "C:\\Program Files\\Open Steno Project\\Plover 5.0.0.dev1"
        - "C:\\Portable Applications"
        - "C:\\Portable Applications\\yt-dlp"
        - "C:\\Program Files\\Calibre2\\"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "C:\\Portable Applications\\zig"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\pnpm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\"
        - "C:\\Users\\<USER>\\.cargo\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\platform-tools"
        - "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\tools"
        - "C:\\Users\\<USER>\\AppData\\Local\\pnpm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Ollama"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Users\\<USER>\\.deno\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
      CMAKE_INSTALL_PREFIX: "C:/Program Files (x86)/aider-quality-cpp"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "C:/Program Files"
        - "C:/Program Files (x86)"
        - "C:/Program Files/CMake"
        - "C:/Program Files (x86)/aider-quality-cpp"
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:83 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting C compiler ABI info"
    directories:
      source: "C:/Users/<USER>/_Items/Dev/GitHub/scale/aider-quality-cpp/build/CMakeFiles/CMakeScratch/TryCompile-2w2jyo"
      binary: "C:/Users/<USER>/_Items/Dev/GitHub/scale/aider-quality-cpp/build/CMakeFiles/CMakeScratch/TryCompile-2w2jyo"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_C_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/_Items/Dev/GitHub/scale/aider-quality-cpp/build/CMakeFiles/CMakeScratch/TryCompile-2w2jyo'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_e7396.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.13.19+0d9f5a35a for .NET Framework
        Build started 7/10/2025 6:03:10 PM.
        
        Project "C:\\Users\\<USER>\\_Items\\Dev\\GitHub\\scale\\aider-quality-cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-2w2jyo\\cmTC_e7396.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_e7396.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "C:\\Users\\<USER>\\_Items\\Dev\\GitHub\\scale\\aider-quality-cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-2w2jyo\\Debug\\".
          Creating directory "cmTC_e7396.dir\\Debug\\cmTC_e7396.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_e7396.dir\\Debug\\cmTC_e7396.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_e7396.dir\\Debug\\cmTC_e7396.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Fo"cmTC_e7396.dir\\Debug\\\\" /Fd"cmTC_e7396.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-4.1\\Modules\\CMakeCCompilerABI.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.43.34810 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Fo"cmTC_e7396.dir\\Debug\\\\" /Fd"cmTC_e7396.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-4.1\\Modules\\CMakeCCompilerABI.c"
          CMakeCCompilerABI.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\link.exe /OUT:"C:\\Users\\<USER>\\_Items\\Dev\\GitHub\\scale\\aider-quality-cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-2w2jyo\\Debug\\cmTC_e7396.exe" /INCREMENTAL /ILK:"cmTC_e7396.dir\\Debug\\cmTC_e7396.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/Users/<USER>/_Items/Dev/GitHub/scale/aider-quality-cpp/build/CMakeFiles/CMakeScratch/TryCompile-2w2jyo/Debug/cmTC_e7396.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /IMPLIB:"C:/Users/<USER>/_Items/Dev/GitHub/scale/aider-quality-cpp/build/CMakeFiles/CMakeScratch/TryCompile-2w2jyo/Debug/cmTC_e7396.lib" /MACHINE:X64  /machine:x64 cmTC_e7396.dir\\Debug\\CMakeCCompilerABI.obj
          cmTC_e7396.vcxproj -> C:\\Users\\<USER>\\_Items\\Dev\\GitHub\\scale\\aider-quality-cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-2w2jyo\\Debug\\cmTC_e7396.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_e7396.dir\\Debug\\cmTC_e7396.tlog\\unsuccessfulbuild".
          Touching "cmTC_e7396.dir\\Debug\\cmTC_e7396.tlog\\cmTC_e7396.lastbuildstate".
        Done Building Project "C:\\Users\\<USER>\\_Items\\Dev\\GitHub\\scale\\aider-quality-cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-2w2jyo\\cmTC_e7396.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:00.75
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:253 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed C implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|link\\.exe|lld-link(\\.exe)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|link\\.exe|lld-link(\\.exe)?))("|,| |$)]
        linker tool for 'C': C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/HostX64/x64/link.exe
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:299 (cmake_determine_linker_id)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Running the C compiler's linker: "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/HostX64/x64/link.exe" "-v"
      Microsoft (R) Incremental Linker Version 14.43.34810.0
      Copyright (C) Microsoft Corporation.  All rights reserved.
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:83 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "C:/Users/<USER>/_Items/Dev/GitHub/scale/aider-quality-cpp/build/CMakeFiles/CMakeScratch/TryCompile-sj4d9l"
      binary: "C:/Users/<USER>/_Items/Dev/GitHub/scale/aider-quality-cpp/build/CMakeFiles/CMakeScratch/TryCompile-sj4d9l"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /W3 /GR /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_CXX_SCAN_FOR_MODULES: "OFF"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/_Items/Dev/GitHub/scale/aider-quality-cpp/build/CMakeFiles/CMakeScratch/TryCompile-sj4d9l'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_c3aa8.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.13.19+0d9f5a35a for .NET Framework
        Build started 7/10/2025 6:03:14 PM.
        
        Project "C:\\Users\\<USER>\\_Items\\Dev\\GitHub\\scale\\aider-quality-cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-sj4d9l\\cmTC_c3aa8.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_c3aa8.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "C:\\Users\\<USER>\\_Items\\Dev\\GitHub\\scale\\aider-quality-cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-sj4d9l\\Debug\\".
          Creating directory "cmTC_c3aa8.dir\\Debug\\cmTC_c3aa8.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_c3aa8.dir\\Debug\\cmTC_c3aa8.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_c3aa8.dir\\Debug\\cmTC_c3aa8.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /GR /Fo"cmTC_c3aa8.dir\\Debug\\\\" /Fd"cmTC_c3aa8.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TP /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-4.1\\Modules\\CMakeCXXCompilerABI.cpp"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.43.34810 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /GR /Fo"cmTC_c3aa8.dir\\Debug\\\\" /Fd"cmTC_c3aa8.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TP /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-4.1\\Modules\\CMakeCXXCompilerABI.cpp"
          CMakeCXXCompilerABI.cpp
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\link.exe /OUT:"C:\\Users\\<USER>\\_Items\\Dev\\GitHub\\scale\\aider-quality-cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-sj4d9l\\Debug\\cmTC_c3aa8.exe" /INCREMENTAL /ILK:"cmTC_c3aa8.dir\\Debug\\cmTC_c3aa8.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/Users/<USER>/_Items/Dev/GitHub/scale/aider-quality-cpp/build/CMakeFiles/CMakeScratch/TryCompile-sj4d9l/Debug/cmTC_c3aa8.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /IMPLIB:"C:/Users/<USER>/_Items/Dev/GitHub/scale/aider-quality-cpp/build/CMakeFiles/CMakeScratch/TryCompile-sj4d9l/Debug/cmTC_c3aa8.lib" /MACHINE:X64  /machine:x64 cmTC_c3aa8.dir\\Debug\\CMakeCXXCompilerABI.obj
          cmTC_c3aa8.vcxproj -> C:\\Users\\<USER>\\_Items\\Dev\\GitHub\\scale\\aider-quality-cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-sj4d9l\\Debug\\cmTC_c3aa8.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_c3aa8.dir\\Debug\\cmTC_c3aa8.tlog\\unsuccessfulbuild".
          Touching "cmTC_c3aa8.dir\\Debug\\cmTC_c3aa8.tlog\\cmTC_c3aa8.lastbuildstate".
        Done Building Project "C:\\Users\\<USER>\\_Items\\Dev\\GitHub\\scale\\aider-quality-cpp\\build\\CMakeFiles\\CMakeScratch\\TryCompile-sj4d9l\\cmTC_c3aa8.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:01.42
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:253 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed CXX implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|link\\.exe|lld-link(\\.exe)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|link\\.exe|lld-link(\\.exe)?))("|,| |$)]
        linker tool for 'CXX': C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/HostX64/x64/link.exe
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:299 (cmake_determine_linker_id)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Running the CXX compiler's linker: "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/HostX64/x64/link.exe" "-v"
      Microsoft (R) Incremental Linker Version 14.43.34810.0
      Copyright (C) Microsoft Corporation.  All rights reserved.
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CTest.cmake:188 (find_program)"
      - "CMakeLists.txt:6 (include)"
    mode: "program"
    variable: "MEMORYCHECK_COMMAND"
    description: "Path to the memory checking command, used for memory error detection."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "purify"
      - "valgrind"
      - "boundscheck"
      - "drmemory"
      - "cuda-memcheck"
      - "compute-sanitizer"
    candidate_directories:
      - "C:/Program Files/Java/jdk-21/bin/"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/ProgramData/chocolatey/bin/"
      - "C:/Program Files/Microsoft VS Code/bin/"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/"
      - "C:/Portable Applications/"
      - "C:/Portable Applications/yt-dlp/"
      - "C:/Program Files/Calibre2/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/Docker/Docker/resources/bin/"
      - "C:/Portable Applications/zig/"
      - "C:/Program Files/nodejs/"
      - "C:/Program Files/dotnet/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/"
      - "C:/Users/<USER>/AppData/Local/pnpm/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/"
      - "C:/Users/<USER>/.cargo/bin/"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Users/<USER>/.deno/bin/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "C:/Program Files/bin/"
      - "C:/Program Files/sbin/"
      - "C:/Program Files/"
      - "C:/Program Files (x86)/bin/"
      - "C:/Program Files (x86)/sbin/"
      - "C:/Program Files (x86)/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Program Files/CMake/sbin/"
      - "C:/Program Files/CMake/"
      - "C:/Program Files (x86)/aider-quality-cpp/bin/"
      - "C:/Program Files (x86)/aider-quality-cpp/sbin/"
      - "C:/Program Files (x86)/aider-quality-cpp/"
      - "/REGISTRY-NOTFOUND/"
    searched_directories:
      - "C:/Program Files/Java/jdk-21/bin/purify.com"
      - "C:/Program Files/Java/jdk-21/bin/purify.exe"
      - "C:/Program Files/Java/jdk-21/bin/purify"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/purify.com"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/purify.exe"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/purify"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/purify.com"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/purify.exe"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/purify"
      - "C:/Windows/System32/purify.com"
      - "C:/Windows/System32/purify.exe"
      - "C:/Windows/System32/purify"
      - "C:/Windows/purify.com"
      - "C:/Windows/purify.exe"
      - "C:/Windows/purify"
      - "C:/Windows/System32/wbem/purify.com"
      - "C:/Windows/System32/wbem/purify.exe"
      - "C:/Windows/System32/wbem/purify"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/purify.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/purify.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/purify"
      - "C:/Windows/System32/OpenSSH/purify.com"
      - "C:/Windows/System32/OpenSSH/purify.exe"
      - "C:/Windows/System32/OpenSSH/purify"
      - "C:/ProgramData/chocolatey/bin/purify.com"
      - "C:/ProgramData/chocolatey/bin/purify.exe"
      - "C:/ProgramData/chocolatey/bin/purify"
      - "C:/Program Files/Microsoft VS Code/bin/purify.com"
      - "C:/Program Files/Microsoft VS Code/bin/purify.exe"
      - "C:/Program Files/Microsoft VS Code/bin/purify"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/purify.com"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/purify.exe"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/purify"
      - "C:/Portable Applications/purify.com"
      - "C:/Portable Applications/purify.exe"
      - "C:/Portable Applications/purify"
      - "C:/Portable Applications/yt-dlp/purify.com"
      - "C:/Portable Applications/yt-dlp/purify.exe"
      - "C:/Portable Applications/yt-dlp/purify"
      - "C:/Program Files/Calibre2/purify.com"
      - "C:/Program Files/Calibre2/purify.exe"
      - "C:/Program Files/Calibre2/purify"
      - "C:/Program Files/Git/cmd/purify.com"
      - "C:/Program Files/Git/cmd/purify.exe"
      - "C:/Program Files/Git/cmd/purify"
      - "C:/Program Files/Docker/Docker/resources/bin/purify.com"
      - "C:/Program Files/Docker/Docker/resources/bin/purify.exe"
      - "C:/Program Files/Docker/Docker/resources/bin/purify"
      - "C:/Portable Applications/zig/purify.com"
      - "C:/Portable Applications/zig/purify.exe"
      - "C:/Portable Applications/zig/purify"
      - "C:/Program Files/nodejs/purify.com"
      - "C:/Program Files/nodejs/purify.exe"
      - "C:/Program Files/nodejs/purify"
      - "C:/Program Files/dotnet/purify.com"
      - "C:/Program Files/dotnet/purify.exe"
      - "C:/Program Files/dotnet/purify"
      - "C:/Program Files/CMake/bin/purify.com"
      - "C:/Program Files/CMake/bin/purify.exe"
      - "C:/Program Files/CMake/bin/purify"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/purify.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/purify.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/purify"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/purify.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/purify.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/purify"
      - "C:/Users/<USER>/AppData/Local/pnpm/purify.com"
      - "C:/Users/<USER>/AppData/Local/pnpm/purify.exe"
      - "C:/Users/<USER>/AppData/Local/pnpm/purify"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/purify.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/purify.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/purify"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/purify.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/purify.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/purify"
      - "C:/Users/<USER>/.cargo/bin/purify.com"
      - "C:/Users/<USER>/.cargo/bin/purify.exe"
      - "C:/Users/<USER>/.cargo/bin/purify"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/purify.com"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/purify.exe"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/purify"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/purify.com"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/purify.exe"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/purify"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/purify.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/purify.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/purify"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/purify.com"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/purify.exe"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/purify"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/purify.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/purify.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/purify"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/purify.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/purify.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/purify"
      - "C:/Users/<USER>/.deno/bin/purify.com"
      - "C:/Users/<USER>/.deno/bin/purify.exe"
      - "C:/Users/<USER>/.deno/bin/purify"
      - "C:/Users/<USER>/AppData/Roaming/npm/purify.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/purify.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/purify"
      - "C:/Program Files/bin/purify.com"
      - "C:/Program Files/bin/purify.exe"
      - "C:/Program Files/bin/purify"
      - "C:/Program Files/sbin/purify.com"
      - "C:/Program Files/sbin/purify.exe"
      - "C:/Program Files/sbin/purify"
      - "C:/Program Files/purify.com"
      - "C:/Program Files/purify.exe"
      - "C:/Program Files/purify"
      - "C:/Program Files (x86)/bin/purify.com"
      - "C:/Program Files (x86)/bin/purify.exe"
      - "C:/Program Files (x86)/bin/purify"
      - "C:/Program Files (x86)/sbin/purify.com"
      - "C:/Program Files (x86)/sbin/purify.exe"
      - "C:/Program Files (x86)/sbin/purify"
      - "C:/Program Files (x86)/purify.com"
      - "C:/Program Files (x86)/purify.exe"
      - "C:/Program Files (x86)/purify"
      - "C:/Program Files/CMake/bin/purify.com"
      - "C:/Program Files/CMake/bin/purify.exe"
      - "C:/Program Files/CMake/bin/purify"
      - "C:/Program Files/CMake/sbin/purify.com"
      - "C:/Program Files/CMake/sbin/purify.exe"
      - "C:/Program Files/CMake/sbin/purify"
      - "C:/Program Files/CMake/purify.com"
      - "C:/Program Files/CMake/purify.exe"
      - "C:/Program Files/CMake/purify"
      - "C:/Program Files (x86)/aider-quality-cpp/bin/purify.com"
      - "C:/Program Files (x86)/aider-quality-cpp/bin/purify.exe"
      - "C:/Program Files (x86)/aider-quality-cpp/bin/purify"
      - "C:/Program Files (x86)/aider-quality-cpp/sbin/purify.com"
      - "C:/Program Files (x86)/aider-quality-cpp/sbin/purify.exe"
      - "C:/Program Files (x86)/aider-quality-cpp/sbin/purify"
      - "C:/Program Files (x86)/aider-quality-cpp/purify.com"
      - "C:/Program Files (x86)/aider-quality-cpp/purify.exe"
      - "C:/Program Files (x86)/aider-quality-cpp/purify"
      - "/REGISTRY-NOTFOUND/purify.com"
      - "/REGISTRY-NOTFOUND/purify.exe"
      - "/REGISTRY-NOTFOUND/purify"
      - "C:/Program Files/Java/jdk-21/bin/valgrind.com"
      - "C:/Program Files/Java/jdk-21/bin/valgrind.exe"
      - "C:/Program Files/Java/jdk-21/bin/valgrind"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/valgrind.com"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/valgrind.exe"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/valgrind"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/valgrind.com"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/valgrind.exe"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/valgrind"
      - "C:/Windows/System32/valgrind.com"
      - "C:/Windows/System32/valgrind.exe"
      - "C:/Windows/System32/valgrind"
      - "C:/Windows/valgrind.com"
      - "C:/Windows/valgrind.exe"
      - "C:/Windows/valgrind"
      - "C:/Windows/System32/wbem/valgrind.com"
      - "C:/Windows/System32/wbem/valgrind.exe"
      - "C:/Windows/System32/wbem/valgrind"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/valgrind.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/valgrind.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/valgrind"
      - "C:/Windows/System32/OpenSSH/valgrind.com"
      - "C:/Windows/System32/OpenSSH/valgrind.exe"
      - "C:/Windows/System32/OpenSSH/valgrind"
      - "C:/ProgramData/chocolatey/bin/valgrind.com"
      - "C:/ProgramData/chocolatey/bin/valgrind.exe"
      - "C:/ProgramData/chocolatey/bin/valgrind"
      - "C:/Program Files/Microsoft VS Code/bin/valgrind.com"
      - "C:/Program Files/Microsoft VS Code/bin/valgrind.exe"
      - "C:/Program Files/Microsoft VS Code/bin/valgrind"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/valgrind.com"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/valgrind.exe"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/valgrind"
      - "C:/Portable Applications/valgrind.com"
      - "C:/Portable Applications/valgrind.exe"
      - "C:/Portable Applications/valgrind"
      - "C:/Portable Applications/yt-dlp/valgrind.com"
      - "C:/Portable Applications/yt-dlp/valgrind.exe"
      - "C:/Portable Applications/yt-dlp/valgrind"
      - "C:/Program Files/Calibre2/valgrind.com"
      - "C:/Program Files/Calibre2/valgrind.exe"
      - "C:/Program Files/Calibre2/valgrind"
      - "C:/Program Files/Git/cmd/valgrind.com"
      - "C:/Program Files/Git/cmd/valgrind.exe"
      - "C:/Program Files/Git/cmd/valgrind"
      - "C:/Program Files/Docker/Docker/resources/bin/valgrind.com"
      - "C:/Program Files/Docker/Docker/resources/bin/valgrind.exe"
      - "C:/Program Files/Docker/Docker/resources/bin/valgrind"
      - "C:/Portable Applications/zig/valgrind.com"
      - "C:/Portable Applications/zig/valgrind.exe"
      - "C:/Portable Applications/zig/valgrind"
      - "C:/Program Files/nodejs/valgrind.com"
      - "C:/Program Files/nodejs/valgrind.exe"
      - "C:/Program Files/nodejs/valgrind"
      - "C:/Program Files/dotnet/valgrind.com"
      - "C:/Program Files/dotnet/valgrind.exe"
      - "C:/Program Files/dotnet/valgrind"
      - "C:/Program Files/CMake/bin/valgrind.com"
      - "C:/Program Files/CMake/bin/valgrind.exe"
      - "C:/Program Files/CMake/bin/valgrind"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/valgrind.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/valgrind.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/valgrind"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/valgrind.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/valgrind.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/valgrind"
      - "C:/Users/<USER>/AppData/Local/pnpm/valgrind.com"
      - "C:/Users/<USER>/AppData/Local/pnpm/valgrind.exe"
      - "C:/Users/<USER>/AppData/Local/pnpm/valgrind"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/valgrind.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/valgrind.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/valgrind"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/valgrind.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/valgrind.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/valgrind"
      - "C:/Users/<USER>/.cargo/bin/valgrind.com"
      - "C:/Users/<USER>/.cargo/bin/valgrind.exe"
      - "C:/Users/<USER>/.cargo/bin/valgrind"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/valgrind.com"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/valgrind.exe"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/valgrind"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/valgrind.com"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/valgrind.exe"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/valgrind"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/valgrind.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/valgrind.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/valgrind"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/valgrind.com"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/valgrind.exe"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/valgrind"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/valgrind.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/valgrind.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/valgrind"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/valgrind.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/valgrind.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/valgrind"
      - "C:/Users/<USER>/.deno/bin/valgrind.com"
      - "C:/Users/<USER>/.deno/bin/valgrind.exe"
      - "C:/Users/<USER>/.deno/bin/valgrind"
      - "C:/Users/<USER>/AppData/Roaming/npm/valgrind.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/valgrind.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/valgrind"
      - "C:/Program Files/bin/valgrind.com"
      - "C:/Program Files/bin/valgrind.exe"
      - "C:/Program Files/bin/valgrind"
      - "C:/Program Files/sbin/valgrind.com"
      - "C:/Program Files/sbin/valgrind.exe"
      - "C:/Program Files/sbin/valgrind"
      - "C:/Program Files/valgrind.com"
      - "C:/Program Files/valgrind.exe"
      - "C:/Program Files/valgrind"
      - "C:/Program Files (x86)/bin/valgrind.com"
      - "C:/Program Files (x86)/bin/valgrind.exe"
      - "C:/Program Files (x86)/bin/valgrind"
      - "C:/Program Files (x86)/sbin/valgrind.com"
      - "C:/Program Files (x86)/sbin/valgrind.exe"
      - "C:/Program Files (x86)/sbin/valgrind"
      - "C:/Program Files (x86)/valgrind.com"
      - "C:/Program Files (x86)/valgrind.exe"
      - "C:/Program Files (x86)/valgrind"
      - "C:/Program Files/CMake/bin/valgrind.com"
      - "C:/Program Files/CMake/bin/valgrind.exe"
      - "C:/Program Files/CMake/bin/valgrind"
      - "C:/Program Files/CMake/sbin/valgrind.com"
      - "C:/Program Files/CMake/sbin/valgrind.exe"
      - "C:/Program Files/CMake/sbin/valgrind"
      - "C:/Program Files/CMake/valgrind.com"
      - "C:/Program Files/CMake/valgrind.exe"
      - "C:/Program Files/CMake/valgrind"
      - "C:/Program Files (x86)/aider-quality-cpp/bin/valgrind.com"
      - "C:/Program Files (x86)/aider-quality-cpp/bin/valgrind.exe"
      - "C:/Program Files (x86)/aider-quality-cpp/bin/valgrind"
      - "C:/Program Files (x86)/aider-quality-cpp/sbin/valgrind.com"
      - "C:/Program Files (x86)/aider-quality-cpp/sbin/valgrind.exe"
      - "C:/Program Files (x86)/aider-quality-cpp/sbin/valgrind"
      - "C:/Program Files (x86)/aider-quality-cpp/valgrind.com"
      - "C:/Program Files (x86)/aider-quality-cpp/valgrind.exe"
      - "C:/Program Files (x86)/aider-quality-cpp/valgrind"
      - "/REGISTRY-NOTFOUND/valgrind.com"
      - "/REGISTRY-NOTFOUND/valgrind.exe"
      - "/REGISTRY-NOTFOUND/valgrind"
      - "C:/Program Files/Java/jdk-21/bin/boundscheck.com"
      - "C:/Program Files/Java/jdk-21/bin/boundscheck.exe"
      - "C:/Program Files/Java/jdk-21/bin/boundscheck"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/boundscheck.com"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/boundscheck.exe"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/boundscheck"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/boundscheck.com"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/boundscheck.exe"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/boundscheck"
      - "C:/Windows/System32/boundscheck.com"
      - "C:/Windows/System32/boundscheck.exe"
      - "C:/Windows/System32/boundscheck"
      - "C:/Windows/boundscheck.com"
      - "C:/Windows/boundscheck.exe"
      - "C:/Windows/boundscheck"
      - "C:/Windows/System32/wbem/boundscheck.com"
      - "C:/Windows/System32/wbem/boundscheck.exe"
      - "C:/Windows/System32/wbem/boundscheck"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/boundscheck.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/boundscheck.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/boundscheck"
      - "C:/Windows/System32/OpenSSH/boundscheck.com"
      - "C:/Windows/System32/OpenSSH/boundscheck.exe"
      - "C:/Windows/System32/OpenSSH/boundscheck"
      - "C:/ProgramData/chocolatey/bin/boundscheck.com"
      - "C:/ProgramData/chocolatey/bin/boundscheck.exe"
      - "C:/ProgramData/chocolatey/bin/boundscheck"
      - "C:/Program Files/Microsoft VS Code/bin/boundscheck.com"
      - "C:/Program Files/Microsoft VS Code/bin/boundscheck.exe"
      - "C:/Program Files/Microsoft VS Code/bin/boundscheck"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/boundscheck.com"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/boundscheck.exe"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/boundscheck"
      - "C:/Portable Applications/boundscheck.com"
      - "C:/Portable Applications/boundscheck.exe"
      - "C:/Portable Applications/boundscheck"
      - "C:/Portable Applications/yt-dlp/boundscheck.com"
      - "C:/Portable Applications/yt-dlp/boundscheck.exe"
      - "C:/Portable Applications/yt-dlp/boundscheck"
      - "C:/Program Files/Calibre2/boundscheck.com"
      - "C:/Program Files/Calibre2/boundscheck.exe"
      - "C:/Program Files/Calibre2/boundscheck"
      - "C:/Program Files/Git/cmd/boundscheck.com"
      - "C:/Program Files/Git/cmd/boundscheck.exe"
      - "C:/Program Files/Git/cmd/boundscheck"
      - "C:/Program Files/Docker/Docker/resources/bin/boundscheck.com"
      - "C:/Program Files/Docker/Docker/resources/bin/boundscheck.exe"
      - "C:/Program Files/Docker/Docker/resources/bin/boundscheck"
      - "C:/Portable Applications/zig/boundscheck.com"
      - "C:/Portable Applications/zig/boundscheck.exe"
      - "C:/Portable Applications/zig/boundscheck"
      - "C:/Program Files/nodejs/boundscheck.com"
      - "C:/Program Files/nodejs/boundscheck.exe"
      - "C:/Program Files/nodejs/boundscheck"
      - "C:/Program Files/dotnet/boundscheck.com"
      - "C:/Program Files/dotnet/boundscheck.exe"
      - "C:/Program Files/dotnet/boundscheck"
      - "C:/Program Files/CMake/bin/boundscheck.com"
      - "C:/Program Files/CMake/bin/boundscheck.exe"
      - "C:/Program Files/CMake/bin/boundscheck"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/boundscheck.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/boundscheck.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/boundscheck"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/boundscheck.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/boundscheck.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/boundscheck"
      - "C:/Users/<USER>/AppData/Local/pnpm/boundscheck.com"
      - "C:/Users/<USER>/AppData/Local/pnpm/boundscheck.exe"
      - "C:/Users/<USER>/AppData/Local/pnpm/boundscheck"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/boundscheck.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/boundscheck.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/boundscheck"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/boundscheck.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/boundscheck.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/boundscheck"
      - "C:/Users/<USER>/.cargo/bin/boundscheck.com"
      - "C:/Users/<USER>/.cargo/bin/boundscheck.exe"
      - "C:/Users/<USER>/.cargo/bin/boundscheck"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/boundscheck.com"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/boundscheck.exe"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/boundscheck"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/boundscheck.com"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/boundscheck.exe"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/boundscheck"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/boundscheck.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/boundscheck.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/boundscheck"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/boundscheck.com"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/boundscheck.exe"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/boundscheck"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/boundscheck.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/boundscheck.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/boundscheck"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/boundscheck.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/boundscheck.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/boundscheck"
      - "C:/Users/<USER>/.deno/bin/boundscheck.com"
      - "C:/Users/<USER>/.deno/bin/boundscheck.exe"
      - "C:/Users/<USER>/.deno/bin/boundscheck"
      - "C:/Users/<USER>/AppData/Roaming/npm/boundscheck.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/boundscheck.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/boundscheck"
      - "C:/Program Files/bin/boundscheck.com"
      - "C:/Program Files/bin/boundscheck.exe"
      - "C:/Program Files/bin/boundscheck"
      - "C:/Program Files/sbin/boundscheck.com"
      - "C:/Program Files/sbin/boundscheck.exe"
      - "C:/Program Files/sbin/boundscheck"
      - "C:/Program Files/boundscheck.com"
      - "C:/Program Files/boundscheck.exe"
      - "C:/Program Files/boundscheck"
      - "C:/Program Files (x86)/bin/boundscheck.com"
      - "C:/Program Files (x86)/bin/boundscheck.exe"
      - "C:/Program Files (x86)/bin/boundscheck"
      - "C:/Program Files (x86)/sbin/boundscheck.com"
      - "C:/Program Files (x86)/sbin/boundscheck.exe"
      - "C:/Program Files (x86)/sbin/boundscheck"
      - "C:/Program Files (x86)/boundscheck.com"
      - "C:/Program Files (x86)/boundscheck.exe"
      - "C:/Program Files (x86)/boundscheck"
      - "C:/Program Files/CMake/bin/boundscheck.com"
      - "C:/Program Files/CMake/bin/boundscheck.exe"
      - "C:/Program Files/CMake/bin/boundscheck"
      - "C:/Program Files/CMake/sbin/boundscheck.com"
      - "C:/Program Files/CMake/sbin/boundscheck.exe"
      - "C:/Program Files/CMake/sbin/boundscheck"
      - "C:/Program Files/CMake/boundscheck.com"
      - "C:/Program Files/CMake/boundscheck.exe"
      - "C:/Program Files/CMake/boundscheck"
      - "C:/Program Files (x86)/aider-quality-cpp/bin/boundscheck.com"
      - "C:/Program Files (x86)/aider-quality-cpp/bin/boundscheck.exe"
      - "C:/Program Files (x86)/aider-quality-cpp/bin/boundscheck"
      - "C:/Program Files (x86)/aider-quality-cpp/sbin/boundscheck.com"
      - "C:/Program Files (x86)/aider-quality-cpp/sbin/boundscheck.exe"
      - "C:/Program Files (x86)/aider-quality-cpp/sbin/boundscheck"
      - "C:/Program Files (x86)/aider-quality-cpp/boundscheck.com"
      - "C:/Program Files (x86)/aider-quality-cpp/boundscheck.exe"
      - "C:/Program Files (x86)/aider-quality-cpp/boundscheck"
      - "/REGISTRY-NOTFOUND/boundscheck.com"
      - "/REGISTRY-NOTFOUND/boundscheck.exe"
      - "/REGISTRY-NOTFOUND/boundscheck"
      - "C:/Program Files/Java/jdk-21/bin/drmemory.com"
      - "C:/Program Files/Java/jdk-21/bin/drmemory.exe"
      - "C:/Program Files/Java/jdk-21/bin/drmemory"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/drmemory.com"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/drmemory.exe"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/drmemory"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/drmemory.com"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/drmemory.exe"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/drmemory"
      - "C:/Windows/System32/drmemory.com"
      - "C:/Windows/System32/drmemory.exe"
      - "C:/Windows/System32/drmemory"
      - "C:/Windows/drmemory.com"
      - "C:/Windows/drmemory.exe"
      - "C:/Windows/drmemory"
      - "C:/Windows/System32/wbem/drmemory.com"
      - "C:/Windows/System32/wbem/drmemory.exe"
      - "C:/Windows/System32/wbem/drmemory"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/drmemory.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/drmemory.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/drmemory"
      - "C:/Windows/System32/OpenSSH/drmemory.com"
      - "C:/Windows/System32/OpenSSH/drmemory.exe"
      - "C:/Windows/System32/OpenSSH/drmemory"
      - "C:/ProgramData/chocolatey/bin/drmemory.com"
      - "C:/ProgramData/chocolatey/bin/drmemory.exe"
      - "C:/ProgramData/chocolatey/bin/drmemory"
      - "C:/Program Files/Microsoft VS Code/bin/drmemory.com"
      - "C:/Program Files/Microsoft VS Code/bin/drmemory.exe"
      - "C:/Program Files/Microsoft VS Code/bin/drmemory"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/drmemory.com"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/drmemory.exe"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/drmemory"
      - "C:/Portable Applications/drmemory.com"
      - "C:/Portable Applications/drmemory.exe"
      - "C:/Portable Applications/drmemory"
      - "C:/Portable Applications/yt-dlp/drmemory.com"
      - "C:/Portable Applications/yt-dlp/drmemory.exe"
      - "C:/Portable Applications/yt-dlp/drmemory"
      - "C:/Program Files/Calibre2/drmemory.com"
      - "C:/Program Files/Calibre2/drmemory.exe"
      - "C:/Program Files/Calibre2/drmemory"
      - "C:/Program Files/Git/cmd/drmemory.com"
      - "C:/Program Files/Git/cmd/drmemory.exe"
      - "C:/Program Files/Git/cmd/drmemory"
      - "C:/Program Files/Docker/Docker/resources/bin/drmemory.com"
      - "C:/Program Files/Docker/Docker/resources/bin/drmemory.exe"
      - "C:/Program Files/Docker/Docker/resources/bin/drmemory"
      - "C:/Portable Applications/zig/drmemory.com"
      - "C:/Portable Applications/zig/drmemory.exe"
      - "C:/Portable Applications/zig/drmemory"
      - "C:/Program Files/nodejs/drmemory.com"
      - "C:/Program Files/nodejs/drmemory.exe"
      - "C:/Program Files/nodejs/drmemory"
      - "C:/Program Files/dotnet/drmemory.com"
      - "C:/Program Files/dotnet/drmemory.exe"
      - "C:/Program Files/dotnet/drmemory"
      - "C:/Program Files/CMake/bin/drmemory.com"
      - "C:/Program Files/CMake/bin/drmemory.exe"
      - "C:/Program Files/CMake/bin/drmemory"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/drmemory.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/drmemory.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/drmemory"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/drmemory.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/drmemory.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/drmemory"
      - "C:/Users/<USER>/AppData/Local/pnpm/drmemory.com"
      - "C:/Users/<USER>/AppData/Local/pnpm/drmemory.exe"
      - "C:/Users/<USER>/AppData/Local/pnpm/drmemory"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/drmemory.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/drmemory.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/drmemory"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/drmemory.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/drmemory.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/drmemory"
      - "C:/Users/<USER>/.cargo/bin/drmemory.com"
      - "C:/Users/<USER>/.cargo/bin/drmemory.exe"
      - "C:/Users/<USER>/.cargo/bin/drmemory"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/drmemory.com"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/drmemory.exe"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/drmemory"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/drmemory.com"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/drmemory.exe"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/drmemory"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/drmemory.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/drmemory.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/drmemory"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/drmemory.com"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/drmemory.exe"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/drmemory"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/drmemory.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/drmemory.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/drmemory"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/drmemory.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/drmemory.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/drmemory"
      - "C:/Users/<USER>/.deno/bin/drmemory.com"
      - "C:/Users/<USER>/.deno/bin/drmemory.exe"
      - "C:/Users/<USER>/.deno/bin/drmemory"
      - "C:/Users/<USER>/AppData/Roaming/npm/drmemory.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/drmemory.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/drmemory"
      - "C:/Program Files/bin/drmemory.com"
      - "C:/Program Files/bin/drmemory.exe"
      - "C:/Program Files/bin/drmemory"
      - "C:/Program Files/sbin/drmemory.com"
      - "C:/Program Files/sbin/drmemory.exe"
      - "C:/Program Files/sbin/drmemory"
      - "C:/Program Files/drmemory.com"
      - "C:/Program Files/drmemory.exe"
      - "C:/Program Files/drmemory"
      - "C:/Program Files (x86)/bin/drmemory.com"
      - "C:/Program Files (x86)/bin/drmemory.exe"
      - "C:/Program Files (x86)/bin/drmemory"
      - "C:/Program Files (x86)/sbin/drmemory.com"
      - "C:/Program Files (x86)/sbin/drmemory.exe"
      - "C:/Program Files (x86)/sbin/drmemory"
      - "C:/Program Files (x86)/drmemory.com"
      - "C:/Program Files (x86)/drmemory.exe"
      - "C:/Program Files (x86)/drmemory"
      - "C:/Program Files/CMake/bin/drmemory.com"
      - "C:/Program Files/CMake/bin/drmemory.exe"
      - "C:/Program Files/CMake/bin/drmemory"
      - "C:/Program Files/CMake/sbin/drmemory.com"
      - "C:/Program Files/CMake/sbin/drmemory.exe"
      - "C:/Program Files/CMake/sbin/drmemory"
      - "C:/Program Files/CMake/drmemory.com"
      - "C:/Program Files/CMake/drmemory.exe"
      - "C:/Program Files/CMake/drmemory"
      - "C:/Program Files (x86)/aider-quality-cpp/bin/drmemory.com"
      - "C:/Program Files (x86)/aider-quality-cpp/bin/drmemory.exe"
      - "C:/Program Files (x86)/aider-quality-cpp/bin/drmemory"
      - "C:/Program Files (x86)/aider-quality-cpp/sbin/drmemory.com"
      - "C:/Program Files (x86)/aider-quality-cpp/sbin/drmemory.exe"
      - "C:/Program Files (x86)/aider-quality-cpp/sbin/drmemory"
      - "C:/Program Files (x86)/aider-quality-cpp/drmemory.com"
      - "C:/Program Files (x86)/aider-quality-cpp/drmemory.exe"
      - "C:/Program Files (x86)/aider-quality-cpp/drmemory"
      - "/REGISTRY-NOTFOUND/drmemory.com"
      - "/REGISTRY-NOTFOUND/drmemory.exe"
      - "/REGISTRY-NOTFOUND/drmemory"
      - "C:/Program Files/Java/jdk-21/bin/cuda-memcheck.com"
      - "C:/Program Files/Java/jdk-21/bin/cuda-memcheck.exe"
      - "C:/Program Files/Java/jdk-21/bin/cuda-memcheck"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/cuda-memcheck.com"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/cuda-memcheck.exe"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/cuda-memcheck"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/cuda-memcheck.com"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/cuda-memcheck.exe"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/cuda-memcheck"
      - "C:/Windows/System32/cuda-memcheck.com"
      - "C:/Windows/System32/cuda-memcheck.exe"
      - "C:/Windows/System32/cuda-memcheck"
      - "C:/Windows/cuda-memcheck.com"
      - "C:/Windows/cuda-memcheck.exe"
      - "C:/Windows/cuda-memcheck"
      - "C:/Windows/System32/wbem/cuda-memcheck.com"
      - "C:/Windows/System32/wbem/cuda-memcheck.exe"
      - "C:/Windows/System32/wbem/cuda-memcheck"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/cuda-memcheck.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/cuda-memcheck.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/cuda-memcheck"
      - "C:/Windows/System32/OpenSSH/cuda-memcheck.com"
      - "C:/Windows/System32/OpenSSH/cuda-memcheck.exe"
      - "C:/Windows/System32/OpenSSH/cuda-memcheck"
      - "C:/ProgramData/chocolatey/bin/cuda-memcheck.com"
      - "C:/ProgramData/chocolatey/bin/cuda-memcheck.exe"
      - "C:/ProgramData/chocolatey/bin/cuda-memcheck"
      - "C:/Program Files/Microsoft VS Code/bin/cuda-memcheck.com"
      - "C:/Program Files/Microsoft VS Code/bin/cuda-memcheck.exe"
      - "C:/Program Files/Microsoft VS Code/bin/cuda-memcheck"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/cuda-memcheck.com"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/cuda-memcheck.exe"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/cuda-memcheck"
      - "C:/Portable Applications/cuda-memcheck.com"
      - "C:/Portable Applications/cuda-memcheck.exe"
      - "C:/Portable Applications/cuda-memcheck"
      - "C:/Portable Applications/yt-dlp/cuda-memcheck.com"
      - "C:/Portable Applications/yt-dlp/cuda-memcheck.exe"
      - "C:/Portable Applications/yt-dlp/cuda-memcheck"
      - "C:/Program Files/Calibre2/cuda-memcheck.com"
      - "C:/Program Files/Calibre2/cuda-memcheck.exe"
      - "C:/Program Files/Calibre2/cuda-memcheck"
      - "C:/Program Files/Git/cmd/cuda-memcheck.com"
      - "C:/Program Files/Git/cmd/cuda-memcheck.exe"
      - "C:/Program Files/Git/cmd/cuda-memcheck"
      - "C:/Program Files/Docker/Docker/resources/bin/cuda-memcheck.com"
      - "C:/Program Files/Docker/Docker/resources/bin/cuda-memcheck.exe"
      - "C:/Program Files/Docker/Docker/resources/bin/cuda-memcheck"
      - "C:/Portable Applications/zig/cuda-memcheck.com"
      - "C:/Portable Applications/zig/cuda-memcheck.exe"
      - "C:/Portable Applications/zig/cuda-memcheck"
      - "C:/Program Files/nodejs/cuda-memcheck.com"
      - "C:/Program Files/nodejs/cuda-memcheck.exe"
      - "C:/Program Files/nodejs/cuda-memcheck"
      - "C:/Program Files/dotnet/cuda-memcheck.com"
      - "C:/Program Files/dotnet/cuda-memcheck.exe"
      - "C:/Program Files/dotnet/cuda-memcheck"
      - "C:/Program Files/CMake/bin/cuda-memcheck.com"
      - "C:/Program Files/CMake/bin/cuda-memcheck.exe"
      - "C:/Program Files/CMake/bin/cuda-memcheck"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/cuda-memcheck.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/cuda-memcheck.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/cuda-memcheck"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/cuda-memcheck.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/cuda-memcheck.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/cuda-memcheck"
      - "C:/Users/<USER>/AppData/Local/pnpm/cuda-memcheck.com"
      - "C:/Users/<USER>/AppData/Local/pnpm/cuda-memcheck.exe"
      - "C:/Users/<USER>/AppData/Local/pnpm/cuda-memcheck"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/cuda-memcheck.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/cuda-memcheck.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/cuda-memcheck"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/cuda-memcheck.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/cuda-memcheck.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/cuda-memcheck"
      - "C:/Users/<USER>/.cargo/bin/cuda-memcheck.com"
      - "C:/Users/<USER>/.cargo/bin/cuda-memcheck.exe"
      - "C:/Users/<USER>/.cargo/bin/cuda-memcheck"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/cuda-memcheck.com"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/cuda-memcheck.exe"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/cuda-memcheck"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/cuda-memcheck.com"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/cuda-memcheck.exe"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/cuda-memcheck"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/cuda-memcheck.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/cuda-memcheck.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/cuda-memcheck"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/cuda-memcheck.com"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/cuda-memcheck.exe"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/cuda-memcheck"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/cuda-memcheck.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/cuda-memcheck.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/cuda-memcheck"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/cuda-memcheck.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/cuda-memcheck.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/cuda-memcheck"
      - "C:/Users/<USER>/.deno/bin/cuda-memcheck.com"
      - "C:/Users/<USER>/.deno/bin/cuda-memcheck.exe"
      - "C:/Users/<USER>/.deno/bin/cuda-memcheck"
      - "C:/Users/<USER>/AppData/Roaming/npm/cuda-memcheck.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/cuda-memcheck.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/cuda-memcheck"
      - "C:/Program Files/bin/cuda-memcheck.com"
      - "C:/Program Files/bin/cuda-memcheck.exe"
      - "C:/Program Files/bin/cuda-memcheck"
      - "C:/Program Files/sbin/cuda-memcheck.com"
      - "C:/Program Files/sbin/cuda-memcheck.exe"
      - "C:/Program Files/sbin/cuda-memcheck"
      - "C:/Program Files/cuda-memcheck.com"
      - "C:/Program Files/cuda-memcheck.exe"
      - "C:/Program Files/cuda-memcheck"
      - "C:/Program Files (x86)/bin/cuda-memcheck.com"
      - "C:/Program Files (x86)/bin/cuda-memcheck.exe"
      - "C:/Program Files (x86)/bin/cuda-memcheck"
      - "C:/Program Files (x86)/sbin/cuda-memcheck.com"
      - "C:/Program Files (x86)/sbin/cuda-memcheck.exe"
      - "C:/Program Files (x86)/sbin/cuda-memcheck"
      - "C:/Program Files (x86)/cuda-memcheck.com"
      - "C:/Program Files (x86)/cuda-memcheck.exe"
      - "C:/Program Files (x86)/cuda-memcheck"
      - "C:/Program Files/CMake/bin/cuda-memcheck.com"
      - "C:/Program Files/CMake/bin/cuda-memcheck.exe"
      - "C:/Program Files/CMake/bin/cuda-memcheck"
      - "C:/Program Files/CMake/sbin/cuda-memcheck.com"
      - "C:/Program Files/CMake/sbin/cuda-memcheck.exe"
      - "C:/Program Files/CMake/sbin/cuda-memcheck"
      - "C:/Program Files/CMake/cuda-memcheck.com"
      - "C:/Program Files/CMake/cuda-memcheck.exe"
      - "C:/Program Files/CMake/cuda-memcheck"
      - "C:/Program Files (x86)/aider-quality-cpp/bin/cuda-memcheck.com"
      - "C:/Program Files (x86)/aider-quality-cpp/bin/cuda-memcheck.exe"
      - "C:/Program Files (x86)/aider-quality-cpp/bin/cuda-memcheck"
      - "C:/Program Files (x86)/aider-quality-cpp/sbin/cuda-memcheck.com"
      - "C:/Program Files (x86)/aider-quality-cpp/sbin/cuda-memcheck.exe"
      - "C:/Program Files (x86)/aider-quality-cpp/sbin/cuda-memcheck"
      - "C:/Program Files (x86)/aider-quality-cpp/cuda-memcheck.com"
      - "C:/Program Files (x86)/aider-quality-cpp/cuda-memcheck.exe"
      - "C:/Program Files (x86)/aider-quality-cpp/cuda-memcheck"
      - "/REGISTRY-NOTFOUND/cuda-memcheck.com"
      - "/REGISTRY-NOTFOUND/cuda-memcheck.exe"
      - "/REGISTRY-NOTFOUND/cuda-memcheck"
      - "C:/Program Files/Java/jdk-21/bin/compute-sanitizer.com"
      - "C:/Program Files/Java/jdk-21/bin/compute-sanitizer.exe"
      - "C:/Program Files/Java/jdk-21/bin/compute-sanitizer"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/compute-sanitizer.com"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/compute-sanitizer.exe"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/compute-sanitizer"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/compute-sanitizer.com"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/compute-sanitizer.exe"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/compute-sanitizer"
      - "C:/Windows/System32/compute-sanitizer.com"
      - "C:/Windows/System32/compute-sanitizer.exe"
      - "C:/Windows/System32/compute-sanitizer"
      - "C:/Windows/compute-sanitizer.com"
      - "C:/Windows/compute-sanitizer.exe"
      - "C:/Windows/compute-sanitizer"
      - "C:/Windows/System32/wbem/compute-sanitizer.com"
      - "C:/Windows/System32/wbem/compute-sanitizer.exe"
      - "C:/Windows/System32/wbem/compute-sanitizer"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/compute-sanitizer.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/compute-sanitizer.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/compute-sanitizer"
      - "C:/Windows/System32/OpenSSH/compute-sanitizer.com"
      - "C:/Windows/System32/OpenSSH/compute-sanitizer.exe"
      - "C:/Windows/System32/OpenSSH/compute-sanitizer"
      - "C:/ProgramData/chocolatey/bin/compute-sanitizer.com"
      - "C:/ProgramData/chocolatey/bin/compute-sanitizer.exe"
      - "C:/ProgramData/chocolatey/bin/compute-sanitizer"
      - "C:/Program Files/Microsoft VS Code/bin/compute-sanitizer.com"
      - "C:/Program Files/Microsoft VS Code/bin/compute-sanitizer.exe"
      - "C:/Program Files/Microsoft VS Code/bin/compute-sanitizer"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/compute-sanitizer.com"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/compute-sanitizer.exe"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/compute-sanitizer"
      - "C:/Portable Applications/compute-sanitizer.com"
      - "C:/Portable Applications/compute-sanitizer.exe"
      - "C:/Portable Applications/compute-sanitizer"
      - "C:/Portable Applications/yt-dlp/compute-sanitizer.com"
      - "C:/Portable Applications/yt-dlp/compute-sanitizer.exe"
      - "C:/Portable Applications/yt-dlp/compute-sanitizer"
      - "C:/Program Files/Calibre2/compute-sanitizer.com"
      - "C:/Program Files/Calibre2/compute-sanitizer.exe"
      - "C:/Program Files/Calibre2/compute-sanitizer"
      - "C:/Program Files/Git/cmd/compute-sanitizer.com"
      - "C:/Program Files/Git/cmd/compute-sanitizer.exe"
      - "C:/Program Files/Git/cmd/compute-sanitizer"
      - "C:/Program Files/Docker/Docker/resources/bin/compute-sanitizer.com"
      - "C:/Program Files/Docker/Docker/resources/bin/compute-sanitizer.exe"
      - "C:/Program Files/Docker/Docker/resources/bin/compute-sanitizer"
      - "C:/Portable Applications/zig/compute-sanitizer.com"
      - "C:/Portable Applications/zig/compute-sanitizer.exe"
      - "C:/Portable Applications/zig/compute-sanitizer"
      - "C:/Program Files/nodejs/compute-sanitizer.com"
      - "C:/Program Files/nodejs/compute-sanitizer.exe"
      - "C:/Program Files/nodejs/compute-sanitizer"
      - "C:/Program Files/dotnet/compute-sanitizer.com"
      - "C:/Program Files/dotnet/compute-sanitizer.exe"
      - "C:/Program Files/dotnet/compute-sanitizer"
      - "C:/Program Files/CMake/bin/compute-sanitizer.com"
      - "C:/Program Files/CMake/bin/compute-sanitizer.exe"
      - "C:/Program Files/CMake/bin/compute-sanitizer"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/compute-sanitizer.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/compute-sanitizer.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/compute-sanitizer"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/compute-sanitizer.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/compute-sanitizer.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/compute-sanitizer"
      - "C:/Users/<USER>/AppData/Local/pnpm/compute-sanitizer.com"
      - "C:/Users/<USER>/AppData/Local/pnpm/compute-sanitizer.exe"
      - "C:/Users/<USER>/AppData/Local/pnpm/compute-sanitizer"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/compute-sanitizer.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/compute-sanitizer.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/compute-sanitizer"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/compute-sanitizer.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/compute-sanitizer.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/compute-sanitizer"
      - "C:/Users/<USER>/.cargo/bin/compute-sanitizer.com"
      - "C:/Users/<USER>/.cargo/bin/compute-sanitizer.exe"
      - "C:/Users/<USER>/.cargo/bin/compute-sanitizer"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/compute-sanitizer.com"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/compute-sanitizer.exe"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/compute-sanitizer"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/compute-sanitizer.com"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/compute-sanitizer.exe"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/compute-sanitizer"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/compute-sanitizer.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/compute-sanitizer.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/compute-sanitizer"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/compute-sanitizer.com"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/compute-sanitizer.exe"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/compute-sanitizer"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/compute-sanitizer.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/compute-sanitizer.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/compute-sanitizer"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/compute-sanitizer.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/compute-sanitizer.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/compute-sanitizer"
      - "C:/Users/<USER>/.deno/bin/compute-sanitizer.com"
      - "C:/Users/<USER>/.deno/bin/compute-sanitizer.exe"
      - "C:/Users/<USER>/.deno/bin/compute-sanitizer"
      - "C:/Users/<USER>/AppData/Roaming/npm/compute-sanitizer.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/compute-sanitizer.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/compute-sanitizer"
      - "C:/Program Files/bin/compute-sanitizer.com"
      - "C:/Program Files/bin/compute-sanitizer.exe"
      - "C:/Program Files/bin/compute-sanitizer"
      - "C:/Program Files/sbin/compute-sanitizer.com"
      - "C:/Program Files/sbin/compute-sanitizer.exe"
      - "C:/Program Files/sbin/compute-sanitizer"
      - "C:/Program Files/compute-sanitizer.com"
      - "C:/Program Files/compute-sanitizer.exe"
      - "C:/Program Files/compute-sanitizer"
      - "C:/Program Files (x86)/bin/compute-sanitizer.com"
      - "C:/Program Files (x86)/bin/compute-sanitizer.exe"
      - "C:/Program Files (x86)/bin/compute-sanitizer"
      - "C:/Program Files (x86)/sbin/compute-sanitizer.com"
      - "C:/Program Files (x86)/sbin/compute-sanitizer.exe"
      - "C:/Program Files (x86)/sbin/compute-sanitizer"
      - "C:/Program Files (x86)/compute-sanitizer.com"
      - "C:/Program Files (x86)/compute-sanitizer.exe"
      - "C:/Program Files (x86)/compute-sanitizer"
      - "C:/Program Files/CMake/bin/compute-sanitizer.com"
      - "C:/Program Files/CMake/bin/compute-sanitizer.exe"
      - "C:/Program Files/CMake/bin/compute-sanitizer"
      - "C:/Program Files/CMake/sbin/compute-sanitizer.com"
      - "C:/Program Files/CMake/sbin/compute-sanitizer.exe"
      - "C:/Program Files/CMake/sbin/compute-sanitizer"
      - "C:/Program Files/CMake/compute-sanitizer.com"
      - "C:/Program Files/CMake/compute-sanitizer.exe"
      - "C:/Program Files/CMake/compute-sanitizer"
      - "C:/Program Files (x86)/aider-quality-cpp/bin/compute-sanitizer.com"
      - "C:/Program Files (x86)/aider-quality-cpp/bin/compute-sanitizer.exe"
      - "C:/Program Files (x86)/aider-quality-cpp/bin/compute-sanitizer"
      - "C:/Program Files (x86)/aider-quality-cpp/sbin/compute-sanitizer.com"
      - "C:/Program Files (x86)/aider-quality-cpp/sbin/compute-sanitizer.exe"
      - "C:/Program Files (x86)/aider-quality-cpp/sbin/compute-sanitizer"
      - "C:/Program Files (x86)/aider-quality-cpp/compute-sanitizer.com"
      - "C:/Program Files (x86)/aider-quality-cpp/compute-sanitizer.exe"
      - "C:/Program Files (x86)/aider-quality-cpp/compute-sanitizer"
      - "/REGISTRY-NOTFOUND/compute-sanitizer.com"
      - "/REGISTRY-NOTFOUND/compute-sanitizer.exe"
      - "/REGISTRY-NOTFOUND/compute-sanitizer"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\Program Files\\Java\\jdk-21\\bin"
        - "C:\\Program Files\\Common Files\\Oracle\\Java\\javapath"
        - "C:\\Program Files\\Microsoft\\jdk-*********-hotspot\\bin"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\ProgramData\\chocolatey\\bin"
        - "C:\\Program Files\\Microsoft VS Code\\bin"
        - "C:\\Program Files\\Open Steno Project\\Plover 5.0.0.dev1"
        - "C:\\Portable Applications"
        - "C:\\Portable Applications\\yt-dlp"
        - "C:\\Program Files\\Calibre2\\"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "C:\\Portable Applications\\zig"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\pnpm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\"
        - "C:\\Users\\<USER>\\.cargo\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\platform-tools"
        - "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\tools"
        - "C:\\Users\\<USER>\\AppData\\Local\\pnpm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Ollama"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Users\\<USER>\\.deno\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
      CMAKE_INSTALL_PREFIX: "C:/Program Files (x86)/aider-quality-cpp"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "C:/Program Files"
        - "C:/Program Files (x86)"
        - "C:/Program Files/CMake"
        - "C:/Program Files (x86)/aider-quality-cpp"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CTest.cmake:196 (find_program)"
      - "CMakeLists.txt:6 (include)"
    mode: "program"
    variable: "COVERAGE_COMMAND"
    description: "Path to the coverage program that CTest uses for performing coverage inspection"
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "gcov"
    candidate_directories:
      - "C:/Program Files/Java/jdk-21/bin/"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/ProgramData/chocolatey/bin/"
      - "C:/Program Files/Microsoft VS Code/bin/"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/"
      - "C:/Portable Applications/"
      - "C:/Portable Applications/yt-dlp/"
      - "C:/Program Files/Calibre2/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/Docker/Docker/resources/bin/"
      - "C:/Portable Applications/zig/"
      - "C:/Program Files/nodejs/"
      - "C:/Program Files/dotnet/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/"
      - "C:/Users/<USER>/AppData/Local/pnpm/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/"
      - "C:/Users/<USER>/.cargo/bin/"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Users/<USER>/.deno/bin/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "C:/Program Files/bin/"
      - "C:/Program Files/sbin/"
      - "C:/Program Files/"
      - "C:/Program Files (x86)/bin/"
      - "C:/Program Files (x86)/sbin/"
      - "C:/Program Files (x86)/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Program Files/CMake/sbin/"
      - "C:/Program Files/CMake/"
      - "C:/Program Files (x86)/aider-quality-cpp/bin/"
      - "C:/Program Files (x86)/aider-quality-cpp/sbin/"
      - "C:/Program Files (x86)/aider-quality-cpp/"
    searched_directories:
      - "C:/Program Files/Java/jdk-21/bin/gcov.com"
      - "C:/Program Files/Java/jdk-21/bin/gcov.exe"
      - "C:/Program Files/Java/jdk-21/bin/gcov"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/gcov.com"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/gcov.exe"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/gcov"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/gcov.com"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/gcov.exe"
      - "C:/Program Files/Microsoft/jdk-*********-hotspot/bin/gcov"
      - "C:/Windows/System32/gcov.com"
      - "C:/Windows/System32/gcov.exe"
      - "C:/Windows/System32/gcov"
      - "C:/Windows/gcov.com"
      - "C:/Windows/gcov.exe"
      - "C:/Windows/gcov"
      - "C:/Windows/System32/wbem/gcov.com"
      - "C:/Windows/System32/wbem/gcov.exe"
      - "C:/Windows/System32/wbem/gcov"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcov.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcov.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcov"
      - "C:/Windows/System32/OpenSSH/gcov.com"
      - "C:/Windows/System32/OpenSSH/gcov.exe"
      - "C:/Windows/System32/OpenSSH/gcov"
      - "C:/ProgramData/chocolatey/bin/gcov.com"
      - "C:/ProgramData/chocolatey/bin/gcov.exe"
      - "C:/ProgramData/chocolatey/bin/gcov"
      - "C:/Program Files/Microsoft VS Code/bin/gcov.com"
      - "C:/Program Files/Microsoft VS Code/bin/gcov.exe"
      - "C:/Program Files/Microsoft VS Code/bin/gcov"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/gcov.com"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/gcov.exe"
      - "C:/Program Files/Open Steno Project/Plover 5.0.0.dev1/gcov"
      - "C:/Portable Applications/gcov.com"
      - "C:/Portable Applications/gcov.exe"
      - "C:/Portable Applications/gcov"
      - "C:/Portable Applications/yt-dlp/gcov.com"
      - "C:/Portable Applications/yt-dlp/gcov.exe"
      - "C:/Portable Applications/yt-dlp/gcov"
      - "C:/Program Files/Calibre2/gcov.com"
      - "C:/Program Files/Calibre2/gcov.exe"
      - "C:/Program Files/Calibre2/gcov"
      - "C:/Program Files/Git/cmd/gcov.com"
      - "C:/Program Files/Git/cmd/gcov.exe"
      - "C:/Program Files/Git/cmd/gcov"
      - "C:/Program Files/Docker/Docker/resources/bin/gcov.com"
      - "C:/Program Files/Docker/Docker/resources/bin/gcov.exe"
      - "C:/Program Files/Docker/Docker/resources/bin/gcov"
      - "C:/Portable Applications/zig/gcov.com"
      - "C:/Portable Applications/zig/gcov.exe"
      - "C:/Portable Applications/zig/gcov"
      - "C:/Program Files/nodejs/gcov.com"
      - "C:/Program Files/nodejs/gcov.exe"
      - "C:/Program Files/nodejs/gcov"
      - "C:/Program Files/dotnet/gcov.com"
      - "C:/Program Files/dotnet/gcov.exe"
      - "C:/Program Files/dotnet/gcov"
      - "C:/Program Files/CMake/bin/gcov.com"
      - "C:/Program Files/CMake/bin/gcov.exe"
      - "C:/Program Files/CMake/bin/gcov"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/gcov.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/gcov.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/gcov"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/gcov.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/gcov.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/gcov"
      - "C:/Users/<USER>/AppData/Local/pnpm/gcov.com"
      - "C:/Users/<USER>/AppData/Local/pnpm/gcov.exe"
      - "C:/Users/<USER>/AppData/Local/pnpm/gcov"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/gcov.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/gcov.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Scripts/gcov"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/gcov.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/gcov.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python312/gcov"
      - "C:/Users/<USER>/.cargo/bin/gcov.com"
      - "C:/Users/<USER>/.cargo/bin/gcov.exe"
      - "C:/Users/<USER>/.cargo/bin/gcov"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/gcov.com"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/gcov.exe"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/gcov"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/gcov.com"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/gcov.exe"
      - "C:/Users/<USER>/AppData/Local/Android/Sdk/tools/gcov"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcov.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcov.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcov"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/gcov.com"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/gcov.exe"
      - "C:/Users/<USER>/AppData/Local/GitHubDesktop/bin/gcov"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/gcov.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/gcov.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Ollama/gcov"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/gcov.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/gcov.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/gcov"
      - "C:/Users/<USER>/.deno/bin/gcov.com"
      - "C:/Users/<USER>/.deno/bin/gcov.exe"
      - "C:/Users/<USER>/.deno/bin/gcov"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcov.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcov.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcov"
      - "C:/Program Files/bin/gcov.com"
      - "C:/Program Files/bin/gcov.exe"
      - "C:/Program Files/bin/gcov"
      - "C:/Program Files/sbin/gcov.com"
      - "C:/Program Files/sbin/gcov.exe"
      - "C:/Program Files/sbin/gcov"
      - "C:/Program Files/gcov.com"
      - "C:/Program Files/gcov.exe"
      - "C:/Program Files/gcov"
      - "C:/Program Files (x86)/bin/gcov.com"
      - "C:/Program Files (x86)/bin/gcov.exe"
      - "C:/Program Files (x86)/bin/gcov"
      - "C:/Program Files (x86)/sbin/gcov.com"
      - "C:/Program Files (x86)/sbin/gcov.exe"
      - "C:/Program Files (x86)/sbin/gcov"
      - "C:/Program Files (x86)/gcov.com"
      - "C:/Program Files (x86)/gcov.exe"
      - "C:/Program Files (x86)/gcov"
      - "C:/Program Files/CMake/bin/gcov.com"
      - "C:/Program Files/CMake/bin/gcov.exe"
      - "C:/Program Files/CMake/bin/gcov"
      - "C:/Program Files/CMake/sbin/gcov.com"
      - "C:/Program Files/CMake/sbin/gcov.exe"
      - "C:/Program Files/CMake/sbin/gcov"
      - "C:/Program Files/CMake/gcov.com"
      - "C:/Program Files/CMake/gcov.exe"
      - "C:/Program Files/CMake/gcov"
      - "C:/Program Files (x86)/aider-quality-cpp/bin/gcov.com"
      - "C:/Program Files (x86)/aider-quality-cpp/bin/gcov.exe"
      - "C:/Program Files (x86)/aider-quality-cpp/bin/gcov"
      - "C:/Program Files (x86)/aider-quality-cpp/sbin/gcov.com"
      - "C:/Program Files (x86)/aider-quality-cpp/sbin/gcov.exe"
      - "C:/Program Files (x86)/aider-quality-cpp/sbin/gcov"
      - "C:/Program Files (x86)/aider-quality-cpp/gcov.com"
      - "C:/Program Files (x86)/aider-quality-cpp/gcov.exe"
      - "C:/Program Files (x86)/aider-quality-cpp/gcov"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\Program Files\\Java\\jdk-21\\bin"
        - "C:\\Program Files\\Common Files\\Oracle\\Java\\javapath"
        - "C:\\Program Files\\Microsoft\\jdk-*********-hotspot\\bin"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\ProgramData\\chocolatey\\bin"
        - "C:\\Program Files\\Microsoft VS Code\\bin"
        - "C:\\Program Files\\Open Steno Project\\Plover 5.0.0.dev1"
        - "C:\\Portable Applications"
        - "C:\\Portable Applications\\yt-dlp"
        - "C:\\Program Files\\Calibre2\\"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "C:\\Portable Applications\\zig"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\pnpm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\"
        - "C:\\Users\\<USER>\\.cargo\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\platform-tools"
        - "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\tools"
        - "C:\\Users\\<USER>\\AppData\\Local\\pnpm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Ollama"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Users\\<USER>\\.deno\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
      CMAKE_INSTALL_PREFIX: "C:/Program Files (x86)/aider-quality-cpp"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "C:/Program Files"
        - "C:/Program Files (x86)"
        - "C:/Program Files/CMake"
        - "C:/Program Files (x86)/aider-quality-cpp"
...
