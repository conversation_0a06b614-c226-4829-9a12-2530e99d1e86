import java.util.*;

class Solution {
    private void flip(boolean[][] matrix, int y0, int x0, int h0, int w0) {
        for (int y = y0; y < y0 + h0; y++) {
            for (int x = x0; x < x0 + w0; x++) {
                matrix[y][x] = !matrix[y][x];
            }
        }
    }

    private boolean allFalse(boolean[][] matrix, int m, int n) {
        for (int y = 0; y < m; y++) {
            for (int x = 0; x < n; x++) {
                if (matrix[y][x]) {
                    return false;
                }
            }
        }

        return true;
    }

    private int countTrues(boolean[][] matrix, int m, int n) {
        int count = 0;
        for (int y = 0; y < m; y++) {
            for (int x = 0; x < n; x++) {
                if (matrix[y][x]) {
                    count++;
                }
            }
        }

        return count;
    }

    private int solveSub(int m, int n, boolean[][] matrix, int[][] prefix, int nCurRects, int curMin) {
        if (allFalse(matrix, m, n)) {
            return nCurRects;
        }

        if (nCurRects >= curMin - 1) {
            return curMin;
        }

        for (int h = 1; h <= m; h++) {
            for (int w = 1; w <= n; w++) {
                for (int y = 0; y <= m - h; y++) {
                    for (int x = 0; x <= n - w; x++) {
                        if (!hasMoreOrEqualTruesThanFalses(y, x, h, w, prefix)) continue;

                        flip(matrix, y, x, h, w);
                        int foundCount = solveSub(m, n, matrix, prefix, nCurRects + 1, curMin);
                        flip(matrix, y, x, h, w);

                        if (foundCount < curMin) {
                            curMin = foundCount;
                        }
                    }
                }
            }
        }

        return curMin;
    }

    private int[][] truePrefix(int m, int n, boolean[][] matrix) {
        int[][] prefix = new int[m + 1][n + 1];

        for (int y = 0; y < m; y++) {
            for (int x = 0; x < n; x++) {
                if (matrix[y][x]) {
                    prefix[y + 1][x + 1] = 1
                        + (x > 0 ? prefix[y + 1][x] : 0)
                        + (y > 0 ? prefix[y][x + 1] : 0);
                }
            }
        }

        return prefix;
    }

    private boolean hasMoreOrEqualTruesThanFalses(int y, int x, int h, int w, int[][] prefix) {
        int nTrues = prefix[y + h][x + w]
            - prefix[y][x + w]
            - prefix[y + h][x]
            + prefix[y][x];

        return 2 * nTrues >= h * w;
    }

    public int solve(int m, int n, boolean[][] matrix) {
        return solveSub(m, n, matrix, truePrefix(m, n, matrix), 0, countTrues(matrix, m, n));
    }
}