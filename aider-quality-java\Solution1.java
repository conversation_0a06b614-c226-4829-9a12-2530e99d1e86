import java.util.*;

class Solution1 {
    private class Rect {
        public int x;
        public int y;
        public int w;
        public int h;

        public Rect(int x, int y, int w, int h) {
            this.x = x;
            this.y = y;
            this.w = w;
            this.h = h;
        }

        public Rect clone() {
            return new Rect(this.x, this.y, this.w, this.h);
        }
    }

    private class Cell {
        public ArrayList<Rect> rects;

        public Cell() {
            this.rects = new ArrayList<>();
        }

        public Cell(ArrayList<Rect> rects) {
            this.rects = rects;
        }

        public Cell clone() {
            ArrayList<Rect> newRects = new ArrayList<>(this.rects.size());

            for (Rect rect : this.rects) {
                newRects.add(rect.clone());
            }

            return new Cell(newRects);
        }
    }

    private Cell createNextCell(Cell prevCell, boolean[][] matrix, int y, int x) {
        if (x == 0 && y == 0) {
            return new Cell();
        }

        Cell newCell = prevCell.clone();

        if (x > 0 && y > 0 && matrix[y - 1][x - 1]) {
            newCell.rects.add(new Rect(y - 1, x - 1, 1, 1));
        }

        return newCell;
    }

    public int solve(int m, int n, boolean[][] matrix) {
        Cell prevCell = null;

        for (int y = 0; y <= m; y++) {
            for (int x = 0; x <= n; x++) {
                Cell newCell = createNextCell(prevCell, matrix, y, x);

                System.out.println(y + " " + x + " " + newCell.rects.size());

                prevCell = newCell;
            }
        }

        return prevCell.rects.size();
    }
}