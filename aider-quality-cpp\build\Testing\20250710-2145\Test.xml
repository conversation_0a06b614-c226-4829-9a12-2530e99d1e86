<?xml version="1.0" encoding="UTF-8"?>
<Site BuildName="Win32-MSBuild"
	BuildStamp="20250710-2145-Experimental"
	Name="lenovoYoga-portable-6-SJMS3NH"
	Generator="ctest-4.1.0-rc1"
	CompilerName="C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/cl.exe"
	CompilerVersion="19.43.34810.0"
	OSName="Windows"
	Hostname="lenovoYoga-portable-6-SJMS3NH"
	OSRelease=" Personal"
	OSVersion=" (Build 26100)"
	OSPlatform="AMD64"
	Is64Bits="1"
	VendorString="GenuineIntel"
	VendorID="Intel Corporation"
	FamilyID="6"
	ModelID="10"
	ModelName=""
	ProcessorCacheSize="-1"
	NumberOfLogicalCPU="16"
	NumberOfPhysicalCPU="12"
	TotalVirtualMemory="37612"
	TotalPhysicalMemory="16108"
	LogicalProcessorsPerPhysical="1"
	ProcessorClockFrequency="2100"
	>
	<Testing>
		<StartDateTime>Jul 10 18:03 Eastern Daylight Time</StartDateTime>
		<StartTestTime>1752185000</StartTestTime>
		<TestList/>
		<EndDateTime>Jul 10 18:03 Eastern Daylight Time</EndDateTime>
		<EndTestTime>1752185000</EndTestTime>
		<ElapsedMinutes>0</ElapsedMinutes>
	</Testing>
</Site>
