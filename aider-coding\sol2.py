def _bal_of_rev_transaction_order(transactions: list[list[int]]) -> int:
    cur_min = 0
    for withdrawal_amount, minimum_required_balance in transactions:
        cur_min = max(cur_min + withdrawal_amount, minimum_required_balance)
    return cur_min

def min_initial_balance(transactions: list[list[int]]) -> int:
    best_order: list[list[int]] = []
    min_bal = 0

    for transaction in transactions:
        candidate_order = [transaction, *best_order]
        best_candidate_order = candidate_order[:]
        new_min_bal = _bal_of_rev_transaction_order(candidate_order)

        for new_pos in range(len(candidate_order) - 1):
            candidate_order[new_pos], candidate_order[new_pos + 1] = candidate_order[new_pos + 1], candidate_order[new_pos]
            candidate_bal = _bal_of_rev_transaction_order(candidate_order)
            if candidate_bal < new_min_bal: 
                best_candidate_order = candidate_order[:]
                new_min_bal = candidate_bal

        best_order = best_candidate_order
        min_bal = new_min_bal

    return min_bal
