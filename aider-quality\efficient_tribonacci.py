from typing import List

def tribonacci(n: int) -> int:
    if not isinstance(n, int):
        raise ValueError("Input must be an integer.")
    if n < 0:
        raise ValueError("Input must be a non-negative integer.")
    if n == 0 or n == 1:
        return 0
    if n == 2:
        return 1
    
    # Initial state vector
    state = [1, 0, 0]
    
    # Transformation matrix M
    matrix = [[1, 1, 1], [1, 0, 0], [0, 1, 0]]
    
    # Compute M^(n-2) and apply to the initial state vector
    result_matrix = _matrix_power(matrix, n - 2)
    result_state = _matrix_multiply_vector(result_matrix, state)
    
    return result_state[0]

def _matrix_multiply(a: List[List[int]], b: List[List[int]]) -> List[List[int]]:
    n = len(a)
    result = [[0 for _ in range(n)] for _ in range(n)]
    for i in range(n):
        for j in range(n):
            for k in range(n):
                result[i][j] += a[i][k] * b[k][j]
    return result

def _matrix_power(matrix: List[List[int]], power: int) -> List[List[int]]:
    if not isinstance(power, int):
        raise TypeError("Power must be an integer.")
    if power < 0:
        raise ValueError("Power must be a non-negative integer.")
    n = len(matrix)
    if power == 0:
        return [[1 if i == j else 0 for j in range(n)] for i in range(n)]
    if power == 1:
        return matrix
    
    half_power = _matrix_power(matrix, power // 2)
    if power % 2 == 0:
        return _matrix_multiply(half_power, half_power)
    else:
        return _matrix_multiply(_matrix_multiply(half_power, half_power), matrix)

def _matrix_multiply_vector(matrix: List[List[int]], vector: List[int]) -> List[int]:
    n = len(matrix)
    result = [0 for _ in range(n)]
    for i in range(n):
        for j in range(n):
            result[i] += matrix[i][j] * vector[j]
    return result