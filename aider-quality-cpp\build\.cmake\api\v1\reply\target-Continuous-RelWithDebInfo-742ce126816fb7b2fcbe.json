{"backtrace": 5, "backtraceGraph": {"commands": ["add_custom_target", "include"], "files": ["C:/Program Files/CMake/share/cmake-4.1/Modules/CTestTargets.cmake", "C:/Program Files/CMake/share/cmake-4.1/Modules/CTest.cmake", "CMakeLists.txt"], "nodes": [{"file": 2}, {"command": 1, "file": 2, "line": 6, "parent": 0}, {"file": 1, "parent": 1}, {"command": 1, "file": 1, "line": 277, "parent": 2}, {"file": 0, "parent": 3}, {"command": 0, "file": 0, "line": 78, "parent": 4}]}, "dependencies": [{"id": "ZERO_CHECK::@6890427a1f51a3e7e1df"}], "folder": {"name": "CTestDashboardTargets"}, "id": "Continuous::@6890427a1f51a3e7e1df", "name": "Continuous", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "", "sourceIndexes": [0]}, {"name": "CMake Rules", "sourceIndexes": [1]}], "sources": [{"backtrace": 5, "isGenerated": true, "path": "build/CMakeFiles/Continuous", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "build/CMakeFiles/63a80b9367caeb690b385e49a64a3cfe/Continuous.rule", "sourceGroupIndex": 1}], "type": "UTILITY"}