import unittest
from efficient_tribonacci import tribonacci, _matrix_multiply, _matrix_power

class TestMatrixOperations(unittest.TestCase):
    def setUp(self):
        self.identity = [
            [1, 0, 0],
            [0, 1, 0],
            [0, 0, 1]
        ]
        self.zero = [
            [0, 0, 0],
            [0, 0, 0],
            [0, 0, 0]
        ]
        self.sample = [
            [1, 2, 3],
            [4, 5, 6],
            [7, 8, 9]
        ]

    def test_matrix_multiply_identity_sample(self):
        result = _matrix_multiply(self.identity, self.sample)
        self.assertEqual(result, self.sample)
        result2 = _matrix_multiply(self.sample, self.identity)
        self.assertEqual(result2, self.sample)

    def test_matrix_multiply_sample_sample(self):
        expected = [
            [30, 36, 42],
            [66, 81, 96],
            [102, 126, 150]
        ]
        result = _matrix_multiply(self.sample, self.sample)
        self.assertEqual(result, expected)

    def test_matrix_multiply_negative_entries(self):
        A = [
            [1, -1, 2],
            [-3, 0, 4],
            [5, 6, -2]
        ]
        B = [
            [-2, 3, 1],
            [4, -5, 6],
            [0, 7, -8]
        ]
        expected = [
            [-6, 22, -21],
            [6, 19, -35],
            [14, -29, 57]
        ]
        result = _matrix_multiply(A, B)
        self.assertEqual(result, expected)

    def test_matrix_power_two(self):
        expected = _matrix_multiply(self.sample, self.sample)
        result = _matrix_power(self.sample, 2)
        self.assertEqual(result, expected)

class TestTribonacci(unittest.TestCase):
    def test_tribonacci_base_cases(self):
        self.assertEqual(tribonacci(0), 0)
        self.assertEqual(tribonacci(1), 0)
        self.assertEqual(tribonacci(2), 1)

    def test_tribonacci_small_values(self):
        known = {
            3: 1,
            4: 2,
            5: 4,
            6: 7,
            7: 13,
            8: 24,
            9: 44,
            10: 81
        }
        for n, expected in known.items():
            self.assertEqual(tribonacci(n), expected)

    def test_tribonacci_against_naive(self):
        def naive(n):
            if n == 0 or n == 1:
                return 0
            if n == 2:
                return 1
            t0, t1, t2 = 0, 0, 1
            for _ in range(3, n + 1):
                t0, t1, t2 = t1, t2, t0 + t1 + t2
            return t2
        for n in range(0, 21):
            self.assertEqual(tribonacci(n), naive(n))
    
    def test_tribonacci_known_larger_index(self):
        self.assertEqual(tribonacci(25), 1389537)

    def test_tribonacci_negative_input(self):
        with self.assertRaises(ValueError):
            tribonacci(-10)

    def test_tribonacci_invalid_input_types(self):
        for invalid in ["5", 3.5, None, [], {}]:
            with self.assertRaises(TypeError):
                tribonacci(invalid)

if __name__ == '__main__':
    unittest.main()