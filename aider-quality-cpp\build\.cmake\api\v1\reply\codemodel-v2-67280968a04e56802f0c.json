{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.10.0"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2, 3, 4, 5, 6]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "aider-quality-cpp", "targetIndexes": [0, 1, 2, 3, 4, 5, 6]}], "targets": [{"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-Debug-c827d4e3c9def2f7ad0a.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 0, "id": "Continuous::@6890427a1f51a3e7e1df", "jsonFile": "target-Continuous-Debug-742ce126816fb7b2fcbe.json", "name": "Continuous", "projectIndex": 0}, {"directoryIndex": 0, "id": "Experimental::@6890427a1f51a3e7e1df", "jsonFile": "target-Experimental-Debug-983a767e7f5e1e57c60f.json", "name": "Experimental", "projectIndex": 0}, {"directoryIndex": 0, "id": "Nightly::@6890427a1f51a3e7e1df", "jsonFile": "target-Nightly-Debug-b1769c17579e8cf0e218.json", "name": "Nightly", "projectIndex": 0}, {"directoryIndex": 0, "id": "NightlyMemoryCheck::@6890427a1f51a3e7e1df", "jsonFile": "target-NightlyMemoryCheck-Debug-821f85d7195a97a1c40e.json", "name": "Nightly<PERSON><PERSON>ory<PERSON><PERSON><PERSON>", "projectIndex": 0}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-Debug-7fb41e09dcbe366eee38.json", "name": "ZERO_CHECK", "projectIndex": 0}, {"directoryIndex": 0, "id": "aider-quality-cpp::@6890427a1f51a3e7e1df", "jsonFile": "target-aider-quality-cpp-Debug-b4f843130f187ec425cc.json", "name": "aider-quality-cpp", "projectIndex": 0}]}, {"directories": [{"build": ".", "jsonFile": "directory-.-Release-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.10.0"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2, 3, 4, 5, 6]}], "name": "Release", "projects": [{"directoryIndexes": [0], "name": "aider-quality-cpp", "targetIndexes": [0, 1, 2, 3, 4, 5, 6]}], "targets": [{"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-Release-c827d4e3c9def2f7ad0a.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 0, "id": "Continuous::@6890427a1f51a3e7e1df", "jsonFile": "target-Continuous-Release-742ce126816fb7b2fcbe.json", "name": "Continuous", "projectIndex": 0}, {"directoryIndex": 0, "id": "Experimental::@6890427a1f51a3e7e1df", "jsonFile": "target-Experimental-Release-983a767e7f5e1e57c60f.json", "name": "Experimental", "projectIndex": 0}, {"directoryIndex": 0, "id": "Nightly::@6890427a1f51a3e7e1df", "jsonFile": "target-Nightly-Release-b1769c17579e8cf0e218.json", "name": "Nightly", "projectIndex": 0}, {"directoryIndex": 0, "id": "NightlyMemoryCheck::@6890427a1f51a3e7e1df", "jsonFile": "target-NightlyMemoryCheck-Release-821f85d7195a97a1c40e.json", "name": "Nightly<PERSON><PERSON>ory<PERSON><PERSON><PERSON>", "projectIndex": 0}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-Release-7fb41e09dcbe366eee38.json", "name": "ZERO_CHECK", "projectIndex": 0}, {"directoryIndex": 0, "id": "aider-quality-cpp::@6890427a1f51a3e7e1df", "jsonFile": "target-aider-quality-cpp-Release-dd107e16f7b1a1cdebbb.json", "name": "aider-quality-cpp", "projectIndex": 0}]}, {"directories": [{"build": ".", "jsonFile": "directory-.-MinSizeRel-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.10.0"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2, 3, 4, 5, 6]}], "name": "MinSizeRel", "projects": [{"directoryIndexes": [0], "name": "aider-quality-cpp", "targetIndexes": [0, 1, 2, 3, 4, 5, 6]}], "targets": [{"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-MinSizeRel-c827d4e3c9def2f7ad0a.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 0, "id": "Continuous::@6890427a1f51a3e7e1df", "jsonFile": "target-Continuous-MinSizeRel-742ce126816fb7b2fcbe.json", "name": "Continuous", "projectIndex": 0}, {"directoryIndex": 0, "id": "Experimental::@6890427a1f51a3e7e1df", "jsonFile": "target-Experimental-MinSizeRel-983a767e7f5e1e57c60f.json", "name": "Experimental", "projectIndex": 0}, {"directoryIndex": 0, "id": "Nightly::@6890427a1f51a3e7e1df", "jsonFile": "target-Nightly-MinSizeRel-b1769c17579e8cf0e218.json", "name": "Nightly", "projectIndex": 0}, {"directoryIndex": 0, "id": "NightlyMemoryCheck::@6890427a1f51a3e7e1df", "jsonFile": "target-NightlyMemoryCheck-MinSizeRel-821f85d7195a97a1c40e.json", "name": "Nightly<PERSON><PERSON>ory<PERSON><PERSON><PERSON>", "projectIndex": 0}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-MinSizeRel-7fb41e09dcbe366eee38.json", "name": "ZERO_CHECK", "projectIndex": 0}, {"directoryIndex": 0, "id": "aider-quality-cpp::@6890427a1f51a3e7e1df", "jsonFile": "target-aider-quality-cpp-MinSizeRel-1454cddd715a32749554.json", "name": "aider-quality-cpp", "projectIndex": 0}]}, {"directories": [{"build": ".", "jsonFile": "directory-.-RelWithDebInfo-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.10.0"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2, 3, 4, 5, 6]}], "name": "RelWithDebInfo", "projects": [{"directoryIndexes": [0], "name": "aider-quality-cpp", "targetIndexes": [0, 1, 2, 3, 4, 5, 6]}], "targets": [{"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-c827d4e3c9def2f7ad0a.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 0, "id": "Continuous::@6890427a1f51a3e7e1df", "jsonFile": "target-Continuous-RelWithDebInfo-742ce126816fb7b2fcbe.json", "name": "Continuous", "projectIndex": 0}, {"directoryIndex": 0, "id": "Experimental::@6890427a1f51a3e7e1df", "jsonFile": "target-Experimental-RelWithDebInfo-983a767e7f5e1e57c60f.json", "name": "Experimental", "projectIndex": 0}, {"directoryIndex": 0, "id": "Nightly::@6890427a1f51a3e7e1df", "jsonFile": "target-Nightly-RelWithDebInfo-b1769c17579e8cf0e218.json", "name": "Nightly", "projectIndex": 0}, {"directoryIndex": 0, "id": "NightlyMemoryCheck::@6890427a1f51a3e7e1df", "jsonFile": "target-NightlyMemoryCheck-RelWithDebInfo-821f85d7195a97a1c40e.json", "name": "Nightly<PERSON><PERSON>ory<PERSON><PERSON><PERSON>", "projectIndex": 0}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-RelWithDebInfo-7fb41e09dcbe366eee38.json", "name": "ZERO_CHECK", "projectIndex": 0}, {"directoryIndex": 0, "id": "aider-quality-cpp::@6890427a1f51a3e7e1df", "jsonFile": "target-aider-quality-cpp-RelWithDebInfo-0a8c9f142d2deb3680d8.json", "name": "aider-quality-cpp", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "C:/Users/<USER>/_Items/Dev/GitHub/scale/aider-quality-cpp/build", "source": "C:/Users/<USER>/_Items/Dev/GitHub/scale/aider-quality-cpp"}, "version": {"major": 2, "minor": 8}}