﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{97401CDE-6D00-31E2-8301-7DBB0924C284}"
	ProjectSection(ProjectDependencies) = postProject
		{1C5E6F25-31CC-3F89-96A8-0B77B7881D0B} = {1C5E6F25-31CC-3F89-96A8-0B77B7881D0B}
		{F8CB753D-37F3-3664-A38F-447B2C067036} = {F8CB753D-37F3-3664-A38F-447B2C067036}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = ".", "..vcxproj", "{1C5E6F25-31CC-3F89-96A8-0B77B7881D0B}"
	ProjectSection(ProjectDependencies) = postProject
		{F8CB753D-37F3-3664-A38F-447B2C067036} = {F8CB753D-37F3-3664-A38F-447B2C067036}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "Continuous", "Continuous.vcxproj", "{F94D70E7-499A-3D9C-98EB-B0F2367C39A1}"
	ProjectSection(ProjectDependencies) = postProject
		{F8CB753D-37F3-3664-A38F-447B2C067036} = {F8CB753D-37F3-3664-A38F-447B2C067036}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "Experimental", "Experimental.vcxproj", "{2972DDF5-D20A-32DC-B895-B1C0885EAEF1}"
	ProjectSection(ProjectDependencies) = postProject
		{F8CB753D-37F3-3664-A38F-447B2C067036} = {F8CB753D-37F3-3664-A38F-447B2C067036}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "Nightly", "Nightly.vcxproj", "{B4926424-1FF8-3A9F-8B82-DFCA85033134}"
	ProjectSection(ProjectDependencies) = postProject
		{F8CB753D-37F3-3664-A38F-447B2C067036} = {F8CB753D-37F3-3664-A38F-447B2C067036}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "NightlyMemoryCheck", "NightlyMemoryCheck.vcxproj", "{691BC437-C795-360C-94DE-0C3A3EA32C7B}"
	ProjectSection(ProjectDependencies) = postProject
		{F8CB753D-37F3-3664-A38F-447B2C067036} = {F8CB753D-37F3-3664-A38F-447B2C067036}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "RUN_TESTS", "RUN_TESTS.vcxproj", "{DFDCAA68-5459-3EEA-BFE6-8726B9388A4C}"
	ProjectSection(ProjectDependencies) = postProject
		{F8CB753D-37F3-3664-A38F-447B2C067036} = {F8CB753D-37F3-3664-A38F-447B2C067036}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "ZERO_CHECK.vcxproj", "{F8CB753D-37F3-3664-A38F-447B2C067036}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
		Release|x64 = Release|x64
		MinSizeRel|x64 = MinSizeRel|x64
		RelWithDebInfo|x64 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{97401CDE-6D00-31E2-8301-7DBB0924C284}.Debug|x64.ActiveCfg = Debug|x64
		{97401CDE-6D00-31E2-8301-7DBB0924C284}.Release|x64.ActiveCfg = Release|x64
		{97401CDE-6D00-31E2-8301-7DBB0924C284}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{97401CDE-6D00-31E2-8301-7DBB0924C284}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{1C5E6F25-31CC-3F89-96A8-0B77B7881D0B}.Debug|x64.ActiveCfg = Debug|x64
		{1C5E6F25-31CC-3F89-96A8-0B77B7881D0B}.Debug|x64.Build.0 = Debug|x64
		{1C5E6F25-31CC-3F89-96A8-0B77B7881D0B}.Release|x64.ActiveCfg = Release|x64
		{1C5E6F25-31CC-3F89-96A8-0B77B7881D0B}.Release|x64.Build.0 = Release|x64
		{1C5E6F25-31CC-3F89-96A8-0B77B7881D0B}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{1C5E6F25-31CC-3F89-96A8-0B77B7881D0B}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{1C5E6F25-31CC-3F89-96A8-0B77B7881D0B}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{1C5E6F25-31CC-3F89-96A8-0B77B7881D0B}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{F94D70E7-499A-3D9C-98EB-B0F2367C39A1}.Debug|x64.ActiveCfg = Debug|x64
		{F94D70E7-499A-3D9C-98EB-B0F2367C39A1}.Release|x64.ActiveCfg = Release|x64
		{F94D70E7-499A-3D9C-98EB-B0F2367C39A1}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{F94D70E7-499A-3D9C-98EB-B0F2367C39A1}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{2972DDF5-D20A-32DC-B895-B1C0885EAEF1}.Debug|x64.ActiveCfg = Debug|x64
		{2972DDF5-D20A-32DC-B895-B1C0885EAEF1}.Release|x64.ActiveCfg = Release|x64
		{2972DDF5-D20A-32DC-B895-B1C0885EAEF1}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{2972DDF5-D20A-32DC-B895-B1C0885EAEF1}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{B4926424-1FF8-3A9F-8B82-DFCA85033134}.Debug|x64.ActiveCfg = Debug|x64
		{B4926424-1FF8-3A9F-8B82-DFCA85033134}.Release|x64.ActiveCfg = Release|x64
		{B4926424-1FF8-3A9F-8B82-DFCA85033134}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{B4926424-1FF8-3A9F-8B82-DFCA85033134}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{691BC437-C795-360C-94DE-0C3A3EA32C7B}.Debug|x64.ActiveCfg = Debug|x64
		{691BC437-C795-360C-94DE-0C3A3EA32C7B}.Release|x64.ActiveCfg = Release|x64
		{691BC437-C795-360C-94DE-0C3A3EA32C7B}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{691BC437-C795-360C-94DE-0C3A3EA32C7B}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{DFDCAA68-5459-3EEA-BFE6-8726B9388A4C}.Debug|x64.ActiveCfg = Debug|x64
		{DFDCAA68-5459-3EEA-BFE6-8726B9388A4C}.Release|x64.ActiveCfg = Release|x64
		{DFDCAA68-5459-3EEA-BFE6-8726B9388A4C}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{DFDCAA68-5459-3EEA-BFE6-8726B9388A4C}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{F8CB753D-37F3-3664-A38F-447B2C067036}.Debug|x64.ActiveCfg = Debug|x64
		{F8CB753D-37F3-3664-A38F-447B2C067036}.Debug|x64.Build.0 = Debug|x64
		{F8CB753D-37F3-3664-A38F-447B2C067036}.Release|x64.ActiveCfg = Release|x64
		{F8CB753D-37F3-3664-A38F-447B2C067036}.Release|x64.Build.0 = Release|x64
		{F8CB753D-37F3-3664-A38F-447B2C067036}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{F8CB753D-37F3-3664-A38F-447B2C067036}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{F8CB753D-37F3-3664-A38F-447B2C067036}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{F8CB753D-37F3-3664-A38F-447B2C067036}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {D2D69174-**************-97F937820491}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal
